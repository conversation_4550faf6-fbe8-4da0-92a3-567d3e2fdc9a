BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082868';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743776';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010445';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010453';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089992';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082869';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010471';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010482';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098284';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098297';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010494';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010512';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098301';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098319';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082870';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098447';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010522';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098451';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010591';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098455';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010661';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089993';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010663';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010672';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098488';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743919';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743968';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743973';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098492';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010764';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010899';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098521';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098525';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010915';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010928';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082873';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098539';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010940';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010978';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010994';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744036';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098595';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098603';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089997';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744039';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098655';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098681';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101011004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744059';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101011027';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744148';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744162';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098727';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098740';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101011045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101011077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744312';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098752';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098769';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098773';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098780';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744337';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100090000';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100090001';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101011086';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100090002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744351';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101011116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100744356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101011132';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090176';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011142';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744382';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098808';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744412';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744495';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744497';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090178';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090179';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098883';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082878';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011159';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744547';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011254';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011284';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011289';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744576';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011323';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744633';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090180';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098936';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098940';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098946';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011348';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744740';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011385';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098964';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098970';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011437';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744772';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744782';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744794';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744825';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744831';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011524';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744848';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011551';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082880';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082881';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101098999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744876';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011574';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011626';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744885';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744889';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744914';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744920';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082883';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099097';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744976';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011693';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100744995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745087';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011728';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099207';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011784';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011820';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011845';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011860';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099263';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011869';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011883';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011899';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011937';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745267';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099282';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099293';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099303';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090184';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099307';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745269';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011976';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011977';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745289';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101011994';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745303';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099362';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099364';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745315';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099367';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082885';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090185';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012038';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099371';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099386';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099405';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012190';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099422';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099436';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012196';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099437';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099455';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099481';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012202';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012214';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099488';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012220';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082886';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090186';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012243';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100090187';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012280';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101012285';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745361';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099502';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA101099519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100082888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:54' WHERE product_id = 'CMA100745490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745524';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745525';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012291';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012314';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099566';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745541';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745549';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082889';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099629';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099631';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012319';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099634';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099642';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099649';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745647';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012400';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099656';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099663';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745700';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745704';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012413';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099680';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012419';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012427';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012431';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099762';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099777';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082890';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090189';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099799';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012438';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745706';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012479';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099811';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745862';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099845';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099849';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099853';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745864';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745870';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745903';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099859';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745904';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012573';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745924';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745937';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099902';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100745964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746052';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746054';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099953';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099956';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099958';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099964';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082892';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746058';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746064';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746080';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090191';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746083';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099972';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099981';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746150';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101099992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746322';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012737';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082897';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746361';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012776';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746362';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012795';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100101';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100104';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746559';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090193';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746572';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012853';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746577';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100117';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746609';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746629';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012893';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746659';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100133';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100142';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012939';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012972';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101012986';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746722';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013045';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090195';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013049';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090196';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013062';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746725';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082900';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100149';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090198';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100174';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100179';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082901';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013132';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013136';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013142';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013150';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013155';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100184';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013203';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013224';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090199';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100082902';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013288';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013333';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013341';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013350';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013356';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101100199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100746800';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013365';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013378';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA101013390';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:55' WHERE product_id = 'CMA100090200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746832';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090201';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013395';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746866';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746867';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746899';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013553';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746929';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746951';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013584';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746972';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746991';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013646';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100746998';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013665';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100277';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082904';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013670';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013732';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747000';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013741';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090202';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013751';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082905';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100301';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090203';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100308';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100309';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090204';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013823';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013864';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747017';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100311';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013896';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013906';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013918';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013944';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013954';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101013962';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014008';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014015';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747045';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014036';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014052';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014068';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747049';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014126';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100323';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100341';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014137';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014160';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082909';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014172';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747091';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014220';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082910';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090397';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014233';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014268';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100359';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747139';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014289';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082911';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090398';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747150';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747156';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082912';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014337';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014344';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014351';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082913';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014359';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747202';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014371';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100395';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014491';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014539';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014549';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014560';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014568';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082914';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100090400';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747334';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101100429';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA101014654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100082915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:56' WHERE product_id = 'CMA100747337';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090401';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014697';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014742';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100457';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100082916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014751';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747371';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747387';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747451';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014785';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100082918';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014879';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014882';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100536';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100546';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090402';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014891';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014897';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014906';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100082920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747604';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747606';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090403';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747617';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747618';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100573';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100582';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014914';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014918';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747647';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100082921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747710';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747739';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014923';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100601';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014934';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100082922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747774';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014966';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747776';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100608';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100641';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090408';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014973';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101014994';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100655';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747781';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090409';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100082923';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015052';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015066';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100082924';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015097';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015125';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747863';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015164';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100083019';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100669';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747889';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015197';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015212';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100684';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747893';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747918';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100083020';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100705';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100706';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015218';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015230';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747950';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100709';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015311';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015329';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090411';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100083021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747990';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100747998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748004';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748013';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100718';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015378';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100083022';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015422';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100090412';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748018';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015444';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748085';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015486';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748111';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015534';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100083023';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015691';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015789';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748113';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748123';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101015907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101016004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100748180';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101016046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA100083024';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101100757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101016141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101016145';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:57' WHERE product_id = 'CMA101016149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016152';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083025';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748224';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748240';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100824';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100831';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100832';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016260';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100846';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748258';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748294';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748298';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016527';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016534';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748344';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748373';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748402';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016602';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016669';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100923';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100945';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090415';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100946';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090416';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748451';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016691';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090417';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016817';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748453';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748486';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090419';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101100978';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748519';
COMMIT;
