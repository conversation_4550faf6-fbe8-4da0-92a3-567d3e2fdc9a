BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405220';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101405306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100822558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100822990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101405893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406554';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101406998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA100823921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:06' WHERE product_id = 'CMA101407115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100823997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101407953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824693';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824848';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101408979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100824992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA101409369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:07' WHERE product_id = 'CMA100825232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100825957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101409997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410017';
COMMIT;
