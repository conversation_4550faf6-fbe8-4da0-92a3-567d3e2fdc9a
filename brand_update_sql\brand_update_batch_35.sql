BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390520';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808693';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808730';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390693';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809785';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100809870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101391732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100809891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100809982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100809987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100809996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101391999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100810990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392785';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA101392996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:54' WHERE product_id = 'CMA100811734';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100811995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101393949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA101394236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:55' WHERE product_id = 'CMA100812847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100812862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100812956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100812974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100812981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100812985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100812996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394606';
COMMIT;
