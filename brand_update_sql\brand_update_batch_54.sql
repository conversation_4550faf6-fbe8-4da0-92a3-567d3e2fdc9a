BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101548984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549220';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101549954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101549965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101549979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101550989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551220';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101551993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101552999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101553973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101554995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101555011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101555022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101555040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:06' WHERE product_id = 'CMA101555063';
COMMIT;
