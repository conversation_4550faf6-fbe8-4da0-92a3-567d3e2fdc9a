import imaplib
import email
import email.header
import datetime
import os
import json
from email.utils import parseaddr
from email.header import decode_header

def decode_str(s):
    """解码字符串，处理多种编码"""
    if not s:
        return ""
    
    result = ""
    for header, charset in decode_header(s):
        if isinstance(header, bytes):
            # 尝试使用指定的字符集
            if charset:
                try:
                    result += header.decode(charset)
                    continue
                except (UnicodeDecodeError, LookupError):
                    pass
            
            # 尝试常见的中文编码
            for encoding in ['utf-8', 'gb18030', 'gbk', 'gb2312', 'iso-8859-1']:
                try:
                    result += header.decode(encoding)
                    break
                except (UnicodeDecodeError, LookupError):
                    continue
            else:
                # 如果所有编码都失败，使用replace模式
                result += header.decode('utf-8', errors='replace')
        else:
            result += header
    
    return result

def get_email_content(msg):
    """获取邮件内容，处理多种编码"""
    content = ""
    html_content = ""
    text_content = ""
    
    # 尝试检测邮件的字符集
    charset = msg.get_content_charset()
    
    if msg.is_multipart():
        # 如果邮件对象是一个MIMEMultipart，获取所有子对象
        for part in msg.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))
            
            # 获取部分的字符集
            part_charset = part.get_content_charset()
            
            # 处理文本内容
            if content_type == "text/plain" and "attachment" not in content_disposition:
                try:
                    payload = part.get_payload(decode=True)
                    # 尝试多种编码
                    for encoding in [part_charset, 'utf-8', 'gb18030', 'gbk', 'gb2312', 'iso-8859-1']:
                        if encoding:
                            try:
                                body = payload.decode(encoding)
                                text_content += body + "\n"
                                break
                            except (UnicodeDecodeError, LookupError):
                                continue
                    if not text_content and payload:
                        # 如果所有编码都失败，使用errors='replace'
                        text_content += payload.decode(errors='replace') + "\n"
                except Exception as e:
                    print(f"解码文本内容时出错: {str(e)}")
            
            elif content_type == "text/html" and "attachment" not in content_disposition:
                try:
                    payload = part.get_payload(decode=True)
                    # 尝试多种编码
                    for encoding in [part_charset, 'utf-8', 'gb18030', 'gbk', 'gb2312', 'iso-8859-1']:
                        if encoding:
                            try:
                                body = payload.decode(encoding)
                                html_content += body + "\n"
                                break
                            except (UnicodeDecodeError, LookupError):
                                continue
                    if not html_content and payload:
                        # 如果所有编码都失败，使用errors='replace'
                        html_content += payload.decode(errors='replace') + "\n"
                except Exception as e:
                    print(f"解码HTML内容时出错: {str(e)}")
    else:
        # 如果邮件对象不是一个MIMEMultipart
        content_type = msg.get_content_type()
        try:
            payload = msg.get_payload(decode=True)
            if content_type == "text/plain":
                # 尝试多种编码
                for encoding in [charset, 'utf-8', 'gb18030', 'gbk', 'gb2312', 'iso-8859-1']:
                    if encoding:
                        try:
                            text_content = payload.decode(encoding)
                            break
                        except (UnicodeDecodeError, LookupError):
                            continue
                if not text_content and payload:
                    text_content = payload.decode(errors='replace')
            
            elif content_type == "text/html":
                # 尝试多种编码
                for encoding in [charset, 'utf-8', 'gb18030', 'gbk', 'gb2312', 'iso-8859-1']:
                    if encoding:
                        try:
                            html_content = payload.decode(encoding)
                            break
                        except (UnicodeDecodeError, LookupError):
                            continue
                if not html_content and payload:
                    html_content = payload.decode(errors='replace')
        except Exception as e:
            print(f"解码内容时出错: {str(e)}")
    
    # 优先使用HTML内容，如果没有则使用纯文本内容
    content = html_content if html_content else text_content
    
    # 如果仍然没有内容，尝试获取任何可能的文本
    if not content and msg.get_payload():
        try:
            if isinstance(msg.get_payload(), list):
                content = "多部分邮件，无法直接显示内容"
            else:
                content = str(msg.get_payload())
        except:
            content = "无法提取邮件内容"
    
    return content

def get_attachments_info(msg):
    """获取附件信息"""
    attachments = []
    
    for part in msg.walk():
        if part.get_content_maintype() == 'multipart':
            continue
        
        if part.get('Content-Disposition') is None:
            continue
            
        filename = part.get_filename()
        if filename:
            # 解码文件名
            filename = decode_str(filename)
            content_type = part.get_content_type()
            
            attachments.append({
                "filename": filename,
                "content_type": content_type,
                "size": len(part.get_payload(decode=True))
            })
    
    return attachments

def save_attachment(msg, save_dir):
    """保存邮件附件"""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    attachments_saved = []
    for part in msg.walk():
        if part.get_content_maintype() == 'multipart':
            continue
        
        if part.get('Content-Disposition') is None:
            continue
            
        filename = part.get_filename()
        if filename:
            # 解码文件名
            filename = decode_str(filename)
            filepath = os.path.join(save_dir, filename)
            
            # 保存附件
            with open(filepath, 'wb') as f:
                f.write(part.get_payload(decode=True))
            print(f"附件 '{filename}' 已保存到 {filepath}")
            attachments_saved.append(filepath)
    
    return attachments_saved

def check_emails(email_account, password, imap_server, imap_port=993, num_emails=5, save_attachments=True):
    """
    检查邮箱并获取最新邮件，保存为JSON文件
    
    参数:
        email_account: 邮箱账号
        password: 邮箱密码或应用专用密码
        imap_server: IMAP服务器地址
        imap_port: IMAP服务器端口，默认993
        num_emails: 获取的邮件数量，默认5封
        save_attachments: 是否保存附件，默认True
    """
    try:
        # 连接到IMAP服务器
        mail = imaplib.IMAP4_SSL(imap_server, imap_port)
        mail.login(email_account, password)
        
        # 选择收件箱
        mail.select('INBOX')
        
        # 搜索所有邮件，按时间降序排列
        status, data = mail.search(None, 'ALL')
        mail_ids = data[0].split()
        
        # 获取最新的几封邮件
        latest_emails = mail_ids[-num_emails:] if len(mail_ids) >= num_emails else mail_ids
        
        print(f"\n获取最新的 {len(latest_emails)} 封邮件：")
        
        # 创建用于存储所有邮件信息的列表
        all_emails = []
        
        # 遍历邮件
        for i, mail_id in enumerate(reversed(latest_emails)):
            status, data = mail.fetch(mail_id, '(RFC822)')
            raw_email = data[0][1]
            
            # 解析邮件
            msg = email.message_from_bytes(raw_email)
            
            # 获取发件人信息
            from_name, from_addr = parseaddr(msg.get('From'))
            from_name = decode_str(from_name)
            
            # 获取主题
            subject = decode_str(msg.get('Subject'))
            
            # 获取日期
            date_str = msg.get('Date')
            
            print(f"\n邮件 {i+1}:")
            print(f"发件人: {from_name} <{from_addr}>")
            print(f"主题: {subject}")
            print(f"日期: {date_str}")
            
            # 获取邮件内容
            content = get_email_content(msg)
            print(f"内容预览: {content[:200]}..." if len(content) > 200 else f"内容: {content}")
            
            # 获取附件信息
            attachments_info = get_attachments_info(msg)
            
            # 保存附件
            attachments_paths = []
            if save_attachments:
                save_dir = f"attachments/{mail_id.decode()}"
                attachments_paths = save_attachment(msg, save_dir)
            
            # 创建邮件信息字典
            email_info = {
                "id": mail_id.decode(),
                "from": {
                    "name": from_name,
                    "address": from_addr
                },
                "subject": subject,
                "date": date_str,
                "content": content,
                "attachments": attachments_info,
                "attachments_saved": attachments_paths
            }
            
            # 添加到邮件列表
            all_emails.append(email_info)
        
        # 关闭连接
        mail.close()
        mail.logout()
        
        # 创建包含所有邮件的字典
        emails_data = {
            "account": email_account,
            "fetch_time": datetime.datetime.now().isoformat(),
            "emails_count": len(all_emails),
            "emails": all_emails
        }
        
        # 保存为JSON文件
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"emails_{timestamp}.json"
        
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(emails_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n所有邮件信息已保存到 {json_filename}")
        
        return json_filename
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return None

def main():
    # 配置信息 - 请替换为您的邮箱信息
    email_account = "<EMAIL>"  # 您的邮箱账号
    password = "DAH2qOw9dKai7Md3"  # 密码或应用专用密码
    
    # 常见邮箱的IMAP服务器设置
    # Gmail: imap.gmail.com
    # Outlook/Hotmail: outlook.office365.com
    # QQ邮箱: imap.qq.com
    # 163邮箱: imap.163.com
    imap_server = "imap.larksuite.com"  # 邮箱的IMAP服务器
    imap_port = 993  # IMAP端口，通常是993
    
    # 获取最新的5封邮件并保存为JSON
    json_file = check_emails(email_account, password, imap_server, imap_port, num_emails=5)
    
    if json_file:
        print(f"邮件数据已成功保存到 {json_file}")
    else:
        print("获取邮件失败")

if __name__ == "__main__":
    main()
