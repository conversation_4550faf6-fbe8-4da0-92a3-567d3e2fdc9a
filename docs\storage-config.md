# 存储服务配置文档

## 数据库表结构

### 品牌表 (brands)

```sql
CREATE TABLE IF NOT EXISTS brands (
    brand_id TEXT PRIMARY KEY,
    full_name TEXT NOT NULL,
    cn_name TEXT,
    en_name TEXT,
    description TEXT,
    category TEXT,
    is_standardized BOOLEAN DEFAULT false
);
```

### 分销商表 (distributors)

```sql
CREATE TABLE IF NOT EXISTS distributors (
    distributor_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    cn_name TEXT,
    en_name TEXT,
    description TEXT,
    category TEXT
);
```

### 产品表 (products)

```sql
CREATE TABLE IF NOT EXISTS products (
    product_id TEXT PRIMARY KEY,
    model TEXT NOT NULL,
    brand_id TEXT NOT NULL,
    price_key TEXT,
    stock_key TEXT,
    datasheet_key TEXT,
    image_keys TEXT,
    parameters JSON,
    vector_data JSON,
    description TEXT,
    updated_at TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(brand_id)
);
```

### 分销商产品表 (distributor_products)

```sql
CREATE TABLE IF NOT EXISTS distributor_products (
    product_id TEXT,
    distributor_id TEXT,
    model TEXT NOT NULL,
    brand_id TEXT NOT NULL,
    price_key TEXT,
    stock_key TEXT,
    datasheet_key TEXT,
    image_keys TEXT,
    parameters JSON,
    vector_data JSON,
    description TEXT,
    updated_at TIMESTAMP,
    PRIMARY KEY (product_id, distributor_id),
    FOREIGN KEY (distributor_id) REFERENCES distributors(distributor_id)
);
```

### 用户表 (users)

```sql
CREATE TABLE IF NOT EXISTS users (
    user_id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 订单表 (orders)

```sql
CREATE TABLE IF NOT EXISTS orders (
    order_id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    status TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

## 索引

```sql
CREATE INDEX IF NOT EXISTS idx_products_brand_id ON products(brand_id);
CREATE INDEX IF NOT EXISTS idx_products_model ON products(model);
CREATE INDEX IF NOT EXISTS idx_distributor_products_distributor_id ON distributor_products(distributor_id);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
```

## 外键关系

1. products 表的 brand_id 关联到 brands 表的 brand_id
2. distributor_products 表的 distributor_id 关联到 distributors 表的 distributor_id
3. orders 表的 user_id 关联到 users 表的 user_id

## 字段说明

### 通用字段
- price_key: 存储在 KV 中的价格数据的键
- stock_key: 存储在 KV 中的库存数据的键
- datasheet_key: 存储在 R2 中的数据表文件的键
- image_keys: 存储在 R2 中的图片文件的键（可能包含多个图片）
- vector_data: 用于向量搜索的数据
- parameters: 产品参数，使用 JSON 格式存储

### 时间戳字段
- created_at: 记录创建时间
- updated_at: 记录更新时间