BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853971';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100854097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101453974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854588';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101454991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA101455003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:39' WHERE product_id = 'CMA100854908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100854995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855554';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101455997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA101456342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:40' WHERE product_id = 'CMA100855764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100855994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101456993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101457997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101458022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101458030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101458057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101458064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101458069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101458075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA101458084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:41' WHERE product_id = 'CMA100856879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100856986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101458995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100857999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA101459892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:42' WHERE product_id = 'CMA100858204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101459912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101459931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101459945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101459963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101459974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101459980';
COMMIT;
