BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739373';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082564';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005207';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089788';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739429';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005208';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392281';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392294';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005209';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739472';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005217';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005233';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739487';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005283';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095119';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005289';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392319';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005351';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739495';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095147';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005414';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739499';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089791';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005428';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005441';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392369';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082566';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005445';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005455';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005459';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005462';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095190';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392377';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392419';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392422';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089792';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392484';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005464';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005484';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005492';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392488';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082567';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739547';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005511';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392576';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392588';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005532';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005535';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095194';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005543';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095285';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392658';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005544';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005550';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005561';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392667';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392679';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005567';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005573';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005578';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082569';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392748';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392764';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095289';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095296';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095299';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095300';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392771';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005584';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089944';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392799';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005612';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005621';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005635';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005641';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005645';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005647';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392806';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005653';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005663';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005688';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005691';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095303';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005710';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095313';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005714';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392812';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005744';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392858';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095323';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095330';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392877';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089945';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392883';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095334';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095367';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095369';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005762';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095376';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005763';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082571';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095409';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095413';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739620';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082572';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095449';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095454';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739633';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082574';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005775';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005804';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005836';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095461';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005849';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005850';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095470';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005853';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089946';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005867';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005880';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095474';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005896';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005921';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095492';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095511';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739659';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005935';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089948';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101005948';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100082575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100082577';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739673';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006059';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095538';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095545';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739694';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006096';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095558';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006167';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006174';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739708';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006203';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089950';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006210';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006230';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095600';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739746';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100082578';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089951';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006311';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095636';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739802';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095641';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006378';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006392';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739819';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100082579';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006405';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089952';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095684';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095689';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095690';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095692';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006444';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006459';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006480';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006482';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739849';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006493';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006524';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100082580';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006555';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006576';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089954';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006598';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095711';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739862';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006611';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100082581';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089955';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739937';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006671';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006687';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739943';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095739';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095759';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100089956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100739952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100740021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100740034';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100740040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100082582';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101006780';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095767';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA101095776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:48' WHERE product_id = 'CMA100740043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006785';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006867';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100089957';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006880';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100082583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006889';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006896';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100082584';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740313';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740316';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100089958';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101095785';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100089959';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006927';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006939';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100082585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740367';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006975';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101095797';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101095808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100082586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101006983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740397';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100089960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740493';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007063';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101095835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740517';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740523';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007078';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007089';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007093';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740535';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740578';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101095845';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101095853';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100089961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100082587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA100740611';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:49' WHERE product_id = 'CMA101007127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740617';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007155';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007162';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007169';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740699';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740725';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740726';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740756';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101095855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100082588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101095896';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100089962';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101095904';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101095913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740759';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007192';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101095916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100082590';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007196';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101095933';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096025';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096039';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100089963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740783';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096043';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096048';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100082591';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096060';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096074';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007211';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007215';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007242';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096093';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100082592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740839';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096098';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007246';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007257';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007267';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740855';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740885';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100082593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740929';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007271';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007274';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007277';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100089964';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007279';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007292';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740935';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007304';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100740993';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096164';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096186';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741027';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096227';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100082721';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741033';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096257';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096262';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096266';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096268';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096271';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096274';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096284';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741048';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007381';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007406';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007427';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100089965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741078';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007467';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096287';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741090';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096295';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741139';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007590';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007610';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101096296';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA101007620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:50' WHERE product_id = 'CMA100741143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741154';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082723';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096297';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741179';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096300';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096376';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096378';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096384';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096388';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741223';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096390';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741236';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096394';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741278';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741284';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741287';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096433';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082725';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741379';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089967';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096509';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096523';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007752';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096528';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007771';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089968';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096531';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096542';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096559';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007776';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096565';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007796';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007818';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096578';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007831';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096585';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007898';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096596';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096605';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007930';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096619';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007956';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096625';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741423';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101007976';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741464';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008011';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741475';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741530';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096654';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741567';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008148';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008175';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741581';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741610';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741623';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741657';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082729';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089970';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096665';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741687';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741782';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741796';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089971';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741833';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096705';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096712';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008368';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082730';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008443';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741984';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096730';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096732';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008460';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096735';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096749';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096763';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100741998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096778';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082731';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096841';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742026';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008516';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742033';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008556';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008557';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089973';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008561';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742059';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742172';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082732';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008578';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742304';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742306';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096893';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008671';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096897';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008679';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008686';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008706';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008714';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008726';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008732';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008741';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008747';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742309';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742331';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742333';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742355';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100082733';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008832';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742367';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742405';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008893';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008901';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100089976';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101096930';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA101008922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:51' WHERE product_id = 'CMA100742406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082734';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101008929';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097020';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089977';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097040';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089978';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082735';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742451';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742464';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097224';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742472';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742487';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009032';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097356';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089980';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097371';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097378';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097383';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742490';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742522';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097408';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082737';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742656';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009096';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097445';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097452';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097464';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009108';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009111';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742673';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082738';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742750';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082739';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097525';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742821';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009132';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742848';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082741';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009171';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009183';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009191';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009217';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097597';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082742';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009223';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742866';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082743';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097617';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742887';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097620';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742910';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009380';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009383';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097661';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009389';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089985';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009398';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009413';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097677';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742955';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097702';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009435';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009450';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100742966';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097728';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743050';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097751';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743061';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743063';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097809';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097821';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097828';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097846';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009510';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082746';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097861';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097893';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097896';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097900';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009520';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743076';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097918';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009531';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009599';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097921';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743123';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009615';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009623';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097950';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097956';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097969';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097975';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009683';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009696';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082747';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743166';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101097982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009853';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009887';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098000';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098010';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098015';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743209';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743220';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009931';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009951';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009955';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009959';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743232';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009971';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009978';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009980';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101009991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743244';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743249';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010101';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010106';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100082749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743284';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100089988';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010112';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010138';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743292';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743310';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098119';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098148';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010150';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101098149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA100743357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:52' WHERE product_id = 'CMA101010190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743359';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743379';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010201';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743415';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743440';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743505';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098176';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082866';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089989';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098181';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098203';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010244';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010251';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743593';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743607';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010289';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010293';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010305';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743686';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100082867';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010380';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089990';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100089991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743726';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010385';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101098225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743737';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010396';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA101010408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:53' WHERE product_id = 'CMA100743748';
COMMIT;
