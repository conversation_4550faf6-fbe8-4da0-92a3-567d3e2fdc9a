BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012099';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494045';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012100';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494048';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545226';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545227';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106085';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102783';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362545';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106098';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106104';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106129';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077523';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100124003';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494053';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106140';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102784';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489704';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106155';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106164';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127063';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106165';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545228';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199863';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025543';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494054';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494092';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545229';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025544';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545230';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100086422';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545231';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106170';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077524';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106175';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362546';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106179';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106191';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106197';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102785';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362547';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127065';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199864';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494095';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094159';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545232';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106199';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106209';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106211';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106214';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106233';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106248';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106255';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362548';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494236';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545233';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545234';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545235';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077525';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100124004';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012101';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106271';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102786';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106282';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127068';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106296';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102787';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106306';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100124005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077526';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102789';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127205';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362550';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094160';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100124006';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545236';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025546';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106314';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199865';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494241';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100086423';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102791';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106321';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489708';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545237';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077527';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127206';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012103';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494245';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025547';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127207';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199866';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362551';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012104';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077528';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489709';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025751';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494247';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489710';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077529';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545239';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106335';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106341';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100086424';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545270';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494315';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100124007';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077530';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106348';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106355';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106362';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106375';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489711';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025752';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094163';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100086425';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127209';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094164';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106382';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106387';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494316';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494317';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102792';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199868';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100086426';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199869';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077531';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494432';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362552';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127211';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106403';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494434';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102793';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012105';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494435';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100025753';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494436';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012106';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086427';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102794';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489712';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012322';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077532';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545271';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545272';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106433';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100025754';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077533';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545274';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102795';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124008';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362554';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012323';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102796';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545275';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106442';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545276';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100199870';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106452';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100199871';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102980';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094165';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094166';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102981';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106481';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106489';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127213';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106503';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100025755';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077534';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106516';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489713';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545277';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100545278';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100199872';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102982';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494440';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100025756';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086428';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077684';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012324';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362556';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100026073';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012325';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124012';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086429';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489845';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086430';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635593';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489846';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489847';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106539';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124015';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100199873';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094168';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077685';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012326';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362557';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489848';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012327';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494441';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124020';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494443';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086431';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106547';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106568';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106586';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012328';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489849';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012329';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489850';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106603';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494445';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100026226';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362737';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489851';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106609';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106632';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489852';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106649';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086432';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106655';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012330';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106680';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106706';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100199874';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106714';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102983';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106716';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077688';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106723';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102985';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106725';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106730';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106736';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106739';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127216';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086433';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077689';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102986';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100026227';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086434';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094169';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127434';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124023';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106741';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106766';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489853';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489854';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635626';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635633';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077690';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102987';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100026228';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127435';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094253';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362738';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086435';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124024';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106769';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635639';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086436';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012331';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489855';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012332';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094254';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100026229';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127436';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100199875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077691';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106773';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106780';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106782';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106787';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106790';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094255';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489856';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100094256';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124030';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100494447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100077692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362739';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100127437';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100199876';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124032';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012333';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100102989';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100124035';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635652';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100012334';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106796';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100086437';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100026230';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489857';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635689';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106798';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106800';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100489858';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106809';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635702';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA100362740';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635714';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101635727';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106821';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106837';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106845';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:08' WHERE product_id = 'CMA101106848';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635730';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635756';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635758';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106851';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106868';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106872';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106873';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635773';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094257';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494448';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635786';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100102990';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362741';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026439';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100199877';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100102991';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100489859';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077693';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100102992';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100086438';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362742';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127438';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106877';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100086439';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635815';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106878';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127439';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635827';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106879';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494449';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077694';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100489860';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127440';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127441';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106883';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127442';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106901';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106903';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635834';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635847';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100199878';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100102993';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100102994';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106906';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100102996';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106909';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106912';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106915';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094258';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106919';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124043';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106927';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635868';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100102997';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635883';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106938';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106946';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103102';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100012335';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103103';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100086440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124048';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103105';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635894';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106952';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106975';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106977';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094259';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106978';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026441';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127443';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103108';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100199879';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103109';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127445';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100200029';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635909';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127446';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100489861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362747';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127447';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362748';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077695';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494451';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124049';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077696';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362750';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077697';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494453';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100086441';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103110';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494454';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100489862';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100012336';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106982';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026627';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100086442';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494455';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094262';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103112';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103114';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103115';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124151';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103116';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100200030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362752';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100086443';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127451';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100200031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077698';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127453';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101106992';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094263';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101107003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077699';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100489863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100494657';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127454';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124156';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101107011';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100026628';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101107014';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100200032';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100489864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100362755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077700';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100200034';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103117';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100200035';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124163';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100103118';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124166';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101107024';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124379';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100094264';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124380';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100124571';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100127456';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA101635956';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100200036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:09' WHERE product_id = 'CMA100077701';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124575';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103119';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494660';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103120';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494661';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107039';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494662';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101635983';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101635999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494663';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100127457';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101636001';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101636015';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100489865';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107048';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100094265';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494668';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107054';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107073';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107080';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107086';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100489866';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107089';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100094266';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101636022';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100127458';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101636027';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200037';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101636035';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362756';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103121';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494676';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100077702';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012337';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124578';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124580';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124581';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124582';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124585';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012338';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124587';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124665';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100096801';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100127648';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086445';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103122';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012339';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103123';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101636452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100077703';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124683';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103124';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200038';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100096802';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107095';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100127649';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086600';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026630';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107099';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100077704';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100489867';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012340';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107107';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012341';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103125';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026631';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362757';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494678';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100489868';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124874';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107112';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100489869';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012343';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100096803';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494682';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494685';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124889';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103126';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494686';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026813';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200040';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026814';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026815';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026816';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494687';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107128';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494689';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124892';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100124896';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494692';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100125049';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100489870';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107145';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200041';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086601';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012344';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100096804';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362758';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494848';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103127';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100127651';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086602';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494849';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100125050';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494857';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100129721';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100096805';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100077706';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362759';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086603';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100490032';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086604';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012346';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107150';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107155';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100494859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100077707';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495033';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200042';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495034';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495036';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100490033';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495037';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026948';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107169';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100129722';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495044';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107177';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200043';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100103264';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107185';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107192';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100490034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362791';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107198';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495045';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100096806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495046';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495050';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100026949';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100129723';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012347';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086605';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100125051';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100077708';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100086607';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107204';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200044';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495053';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200045';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107217';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495054';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100200046';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495057';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362793';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100490035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100362794';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA101107224';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100129724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100077709';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100012348';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100495239';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:10' WHERE product_id = 'CMA100129725';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012349';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086608';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107230';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100026950';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200047';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107237';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096807';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100490036';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125057';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103265';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495241';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362795';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362796';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103266';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103269';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362797';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012350';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200048';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012351';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100077710';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012506';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012507';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495413';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100490037';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096808';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107252';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086609';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100129726';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100026951';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125059';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100026952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100077711';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200050';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100026953';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362798';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200051';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103270';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100490038';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096809';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100026954';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107268';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096810';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100027148';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100490039';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100129729';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495415';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100077712';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495416';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086610';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200052';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103272';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362799';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103304';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100027149';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100077713';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086611';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100490040';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107290';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103305';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096811';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495419';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096812';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100495426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362800';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200053';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496880';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100129935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362802';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496882';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362803';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100490041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362804';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103306';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096813';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107297';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100129937';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100027150';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496883';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125241';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100077944';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012509';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125244';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200054';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125245';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012510';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100077945';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103307';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086612';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107309';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100129938';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496890';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100129939';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100490042';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125426';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086613';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100125427';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107315';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100129940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100077946';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496892';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100096814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362807';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496895';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100027151';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103308';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086614';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012512';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100200055';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100496896';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100012513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100362808';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100103310';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA100086615';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:11' WHERE product_id = 'CMA101107341';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096815';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200056';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490043';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027152';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490044';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107355';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496903';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496906';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027313';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200057';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496911';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200058';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100129942';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077947';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107367';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100362809';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490045';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496912';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100012514';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077948';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103312';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107381';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107390';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130098';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027314';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200059';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107393';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107415';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096817';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103313';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130099';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100125432';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027315';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100086616';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107419';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130104';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096818';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077949';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103316';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100012515';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200060';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100125443';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100125446';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200061';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100012516';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107429';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130105';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496915';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100086617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077950';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103318';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496917';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130106';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100362810';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096819';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027329';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490046';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027513';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130108';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107440';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103319';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200062';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096820';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077952';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100125448';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490048';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490049';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077953';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100496920';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100497109';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100125449';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200063';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100012517';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100362812';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027514';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490050';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107447';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107462';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100086618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077954';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107482';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107489';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100086619';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107494';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130109';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107500';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103322';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100125451';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107514';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100362815';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100362816';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100086620';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103463';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100086621';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100027685';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200064';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100497121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077955';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096822';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490053';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103464';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100042515';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130115';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100086622';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100012518';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107533';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100497128';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100096823';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100042516';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100077956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100362818';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100125691';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100103465';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130118';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107545';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107553';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107558';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107560';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100497134';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100490054';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100012519';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107572';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107576';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA101107583';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100200066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100362819';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:12' WHERE product_id = 'CMA100130120';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100490055';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100103466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100077957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100362820';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100042517';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100125694';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100490056';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100012520';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100200067';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086623';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086624';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107588';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107599';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130121';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100497135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100077958';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100096824';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100200068';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086625';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107609';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107620';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130124';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100497298';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100012521';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100490057';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086626';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086627';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100200069';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100096825';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107627';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100042518';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100125698';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100103467';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086628';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100490058';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107636';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100012522';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100077959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100362992';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100096826';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130125';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107671';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100497302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130126';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100125704';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100490059';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107689';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100042519';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130127';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100125711';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130268';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100012523';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100497313';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100103468';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100200070';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100490060';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100362993';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100200071';
COMMIT;
