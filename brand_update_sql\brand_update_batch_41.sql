BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101420963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100833984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101421962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA101422172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:18' WHERE product_id = 'CMA100834377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100834983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101422989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA100835515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:19' WHERE product_id = 'CMA101423688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423850';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101423996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100835998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA100836320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:20' WHERE product_id = 'CMA101424864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101424986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101425970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100836995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426582';
COMMIT;
