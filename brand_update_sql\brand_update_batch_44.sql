BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439434';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101440007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101440017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101440020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101440034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101440040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100845998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101440995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100846995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101441997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101442002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA101442009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:31' WHERE product_id = 'CMA100847128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442971';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101442993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100847998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101443996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100848995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA101444764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:32' WHERE product_id = 'CMA100849124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101444991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849458';
COMMIT;
