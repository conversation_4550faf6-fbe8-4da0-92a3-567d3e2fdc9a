import requests
import os
import json
import datetime

# Cloudflare API 凭证
# 请替换以下变量为您的实际值，或者保持与原脚本一致
CF_API_TOKEN = "IaSlqJwOLcM2mYHmVi-NjhqP6X35o78uoMZgHFfP"  # 您的 Cloudflare API 令牌
CF_ACCOUNT_ID = "164ebb2bf9333643b2c8232a628a286b"  # 您的 Cloudflare 账户 ID
DATABASE_ID = "404f09be-cb7e-46ed-8c6a-37960f31147e"  # 您的数据库id
DATABASE_NAME = "little-field-db"  # 您的数据库名称

# 创建博客表的 SQL 语句
create_blog_table_sql = """
CREATE TABLE IF NOT EXISTS blog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type INTEGER DEFAULT 0,
    status INTEGER DEFAULT 0,
    title TEXT NOT NULL,
    subtitle TEXT,
    description TEXT,
    cover_image TEXT,
    content_markdown TEXT,
    tags TEXT,
    category TEXT,
    location TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    author_id TEXT NOT NULL
);
"""

# 创建作者表的 SQL 语句
create_author_table_sql = """
CREATE TABLE IF NOT EXISTS author (
    author_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    avatar TEXT,
    description TEXT,
    bio TEXT
);
"""

# 创建专题表的 SQL 语句
create_insights_table_sql = """
CREATE TABLE IF NOT EXISTS insights (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type INTEGER DEFAULT 0,
    status INTEGER DEFAULT 0,
    title TEXT NOT NULL,
    subtitle TEXT,
    description TEXT,
    cover_image TEXT,
    tags TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    author_id TEXT NOT NULL
);
"""

create_insights_blog_map_table_sql = """
CREATE TABLE IF NOT EXISTS insights_blog_map (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    insights_id INTEGER,
    blog_id INTEGER
);
"""

def execute_d1_query(sql):
    """
    在 Cloudflare D1 数据库上执行 SQL 查询
    
    Args:
        sql: 要执行的SQL语句
        
    Returns:
        响应对象
    """
    url = f"https://api.cloudflare.com/client/v4/accounts/{CF_ACCOUNT_ID}/d1/database/{DATABASE_ID}/query"
    
    headers = {
        "Authorization": f"Bearer {CF_API_TOKEN}",
        "Content-Type": "application/json"
    }
    
    data = {
        "sql": sql
    }
    
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        print("SQL 查询执行成功！")
        response_data = response.json()
        print(json.dumps(response_data, ensure_ascii=False, indent=2))
        return response_data
    else:
        print(f"错误: {response.status_code}")
        print(response.text)
        return None

def verify_database_connection():
    """
    验证数据库连接是否正常
    
    Returns:
        连接是否正常
    """
    try:
        # 尝试执行一个简单的查询
        sql = "SELECT 1"
        
        response_data = execute_d1_query(sql)
        
        if response_data and response_data.get("success", False):
            print("数据库连接正常")
            return True
        
        print("数据库连接异常")
        return False
    except Exception as e:
        print(f"验证数据库连接时出错: {e}")
        return False

def main():
    print(f"开始连接到 Cloudflare D1 数据库 {DATABASE_NAME}...")
    
    # 验证数据库连接
    if not verify_database_connection():
        print("数据库连接异常，操作终止")
        return
    
    print("开始创建博客表...")
    
    # 创建博客表
    execute_d1_query(create_blog_table_sql)
    
    print("博客表创建完成。")
    
    print("开始创建作者表...")
    
    # 创建作者表
    execute_d1_query(create_author_table_sql)
    
    print("作者表创建完成。")
    
    print("开始创建专题表...")
    # 创建专题表
    execute_d1_query(create_insights_table_sql)
    print("专题表创建完成。")


    print("开始创建专题博客关联表...")
    # 创建专题表
    execute_d1_query(create_insights_blog_map_table_sql)
    print("专题博客关联表创建完成。")


    # 验证表是否创建成功
    print("验证表是否创建成功...")
    
    # 查询表结构
    blog_table_info = execute_d1_query("PRAGMA table_info(blog)")
    author_table_info = execute_d1_query("PRAGMA table_info(author)")
    insights_table_info = execute_d1_query("PRAGMA table_info(insights)")
    insights_blog_map_table_info = execute_d1_query("PRAGMA table_info(insights_blog_map)")
    if blog_table_info and author_table_info and insights_table_info and insights_blog_map_table_info:
        print("表创建验证成功！")
    else:
        print("表创建验证失败，请检查错误信息。")

if __name__ == "__main__":
    main()