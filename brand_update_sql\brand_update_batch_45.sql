BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849522';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100849998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100850002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100850008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA101445878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:33' WHERE product_id = 'CMA100850012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101445994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446913';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101446999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101447014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA101447030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:34' WHERE product_id = 'CMA100850731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100850981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA100851207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:35' WHERE product_id = 'CMA101447680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101447997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851693';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101448997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851971';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100851999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA100852079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:36' WHERE product_id = 'CMA101449464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101449985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450434';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101450984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100852996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100853012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA101451316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:37' WHERE product_id = 'CMA100853026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101451982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101452994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA100853898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:38' WHERE product_id = 'CMA101453204';
COMMIT;
