import json
import re

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return json.load(file)
    except Exception as e:
        print(f"加载文件 {file_path} 时出错: {e}")
        return None

def find_brand_name_en(brand_id, brands_data):
    """根据brand_id查找品牌的英文名称"""
    for brand in brands_data:
        if brand.get('code') == brand_id:
            return brand.get('name_en', '')
    return ''

def find_category_names(category_code, categories_data):
    """根据三级分类编码查找对应的一二三级分类英文名称"""
    # 初始化返回值
    level1_name = ''
    level2_name = ''
    level3_name = ''
    
    # 遍历一级分类
    for level1 in categories_data:
        if level1['code'] == category_code[:7]:  # 一级分类编码前7位
            level1_name = level1.get('name_en', '')
            
            # 遍历二级分类
            for level2 in level1.get('sub_categories', []):
                if level2['code'] == category_code[:10]:  # 二级分类编码前10位
                    level2_name = level2.get('name_en', '')
                    
                    # 遍历三级分类
                    for level3 in level2.get('items', []):
                        if level3['code'] == category_code:  # 完整的三级分类编码
                            level3_name = level3.get('name_en', '')
                            return level1_name, level2_name, level3_name
    
    return level1_name, level2_name, level3_name

def clean_string(text):
    """处理字符串，将除了"_"之外的所有空格和特殊符号替换为"-"""
    # 保留下划线，其他特殊字符和空格替换为连字符
    cleaned = re.sub(r'[^\w]|[\s]', '-', text)
    # 替换连续的连字符为单个连字符
    cleaned = re.sub(r'-+', '-', cleaned)
    # 保留原始的下划线
    cleaned = cleaned.replace('-_-', '_')
    # 去除开头和结尾的连字符
    cleaned = cleaned.strip('-')
    return cleaned

def main():
    # 1. 加载所需的JSON文件
    products_file = 'new_all_product_json/new_products.json'
    categories_file = 'new_all_product_json/new_category_code copy.json'
    brands_file = 'new_all_product_json/merged_brands_with_category_updated_final.json'
    category_map_file = 'new_all_product_json/new_to_category_code_map.json'
    
    products_data = load_json_file(products_file)
    categories_data = load_json_file(categories_file)
    brands_data = load_json_file(brands_file)
    category_map = load_json_file(category_map_file)
    
    if not all([products_data, categories_data, brands_data, category_map]):
        print("加载文件失败，请检查文件路径和内容")
        return
    
    # 2. 创建输出文件
    output_file = 'product_names.txt'
    
    with open(output_file, 'w', encoding='utf-8') as out_file:
        # 3. 处理每个产品
        for product in products_data:
            product_id = product.get('product_id', '')
            model = product.get('model', '')
            brand_id = product.get('brand_id', '')
            
            # 查找品牌英文名称
            brand_name_en = find_brand_name_en(brand_id, brands_data)
            
            # 查找产品对应的分类编码
            category_code = category_map.get(product_id, '')
            
            if category_code:
                # 查找分类名称
                level1_name, level2_name, level3_name = find_category_names(category_code, categories_data)
                
                # 构建字符串
                result_string = f"{level1_name}-{level2_name}-{level3_name}-{brand_name_en}-{model}-{product_id}"
                
                # 处理字符串
                cleaned_string = clean_string(result_string)
                
                # 写入输出文件
                out_file.write(f"{cleaned_string}\n")
            else:
                print(f"未找到产品 {product_id} 的分类编码")
    
    print(f"处理完成，结果已保存到 {output_file}")

if __name__ == "__main__":
    main()