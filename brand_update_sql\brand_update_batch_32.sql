BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100788997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100789998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101377928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100790222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101377930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101377944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101377953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101377961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101377973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101377993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101377996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378371';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100790994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791913';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101378991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100791998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA101379466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:43' WHERE product_id = 'CMA100792289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101379991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100792988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380220';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA100793729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:44' WHERE product_id = 'CMA101380790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793943';
COMMIT;
