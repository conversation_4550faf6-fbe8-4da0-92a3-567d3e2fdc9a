BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101475996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101476996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101477006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101477025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101477972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101478997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101479995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:51' WHERE product_id = 'CMA101480652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101480994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101481999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101482994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:52' WHERE product_id = 'CMA101483141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101483991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101484999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101485992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101486992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101487981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101488996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489680';
COMMIT;
