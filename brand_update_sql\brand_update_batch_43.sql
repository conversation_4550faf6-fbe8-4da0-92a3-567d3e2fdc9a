BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840785';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840848';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841554';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100841666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101434675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101434981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100841979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435693';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842588';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA100842613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:27' WHERE product_id = 'CMA101435783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101435988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100842997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101436991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA101437435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:28' WHERE product_id = 'CMA100843582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100843993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101437997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438588';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA100844641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:29' WHERE product_id = 'CMA101438643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100844982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101438992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA101439290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:30' WHERE product_id = 'CMA100845163';
COMMIT;
