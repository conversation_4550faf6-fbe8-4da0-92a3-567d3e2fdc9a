BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101386495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100802146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100802894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101386985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA101387548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:50' WHERE product_id = 'CMA100803914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100803969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101387994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100804985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388522';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805554';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100805915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101388997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101389008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101389017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101389026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101389035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA100806575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101389041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:51' WHERE product_id = 'CMA101389066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100806997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807554';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389848';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101389997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA100807953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:52' WHERE product_id = 'CMA101390130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100807961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100807972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100807986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100807991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100807994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA100808065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:53' WHERE product_id = 'CMA101390270';
COMMIT;
