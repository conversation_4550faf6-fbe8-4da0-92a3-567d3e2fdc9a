BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078788';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701714';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493013';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701716';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869142';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869169';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869172';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869173';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493170';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701724';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869184';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869186';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869191';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869196';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100578276';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217107';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869200';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701747';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087266';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101206160';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101206171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869215';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869242';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217108';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078791';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100031314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701782';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701822';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701823';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701825';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493172';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701838';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100578282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701843';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087267';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869267';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869270';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087268';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078792';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217109';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869275';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206175';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869280';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701877';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132352';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869286';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578285';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869308';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100493173';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107411';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701881';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107412';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132353';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701905';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869330';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107415';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043496';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217111';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578286';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031316';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869426';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078794';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869429';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132354';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031318';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100493174';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206189';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087269';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107416';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217112';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701928';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132356';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031319';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701946';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578287';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701962';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100493175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701968';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701976';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100701980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702008';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869505';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869520';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107417';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078795';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043499';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107418';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107419';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869529';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869542';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217113';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078796';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031320';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132357';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043500';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132358';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087270';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702017';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578288';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702071';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869558';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702095';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078799';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107420';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869577';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578291';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206374';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078800';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217114';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702112';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702113';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869620';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869621';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869631';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043502';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100493176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702141';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702150';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702154';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702165';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087271';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578292';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869696';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869719';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869728';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869741';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206421';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217115';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869745';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869747';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043504';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869751';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869754';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206433';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206453';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043505';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087272';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078802';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087273';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702169';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA101206612';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087274';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217116';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043506';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869762';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869776';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578296';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100493177';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100578299';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100043507';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100493178';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031526';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869789';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031527';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132379';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100031528';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100702247';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132381';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100107423';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100132382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100078803';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100869814';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100217117';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:35' WHERE product_id = 'CMA100087275';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578300';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043508';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702253';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217119';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869842';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217120';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100087276';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206629';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869855';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869856';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493179';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107424';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206659';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031529';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702256';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493180';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578303';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493181';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043509';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132504';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043510';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869865';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869882';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206715';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493182';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702317';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702336';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217121';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100087277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702344';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578304';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702351';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031530';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493183';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206877';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702359';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100078804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702398';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206882';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702413';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869885';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702446';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869918';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702549';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100078805';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578307';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217123';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101206978';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107428';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132509';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043512';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100869995';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702622';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132511';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043514';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101207044';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100078806';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870007';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870011';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101207081';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870013';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031532';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870018';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100087278';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132512';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493184';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702694';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702711';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702714';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107429';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870035';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870051';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870054';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493185';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101207126';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132515';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100078808';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870061';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132516';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578317';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217125';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132517';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217126';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043515';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100087279';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870084';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107430';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031533';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493186';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870099';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031534';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031535';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870106';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870112';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100087280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100078809';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870137';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107572';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702809';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043516';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101207231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702891';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702896';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132520';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578441';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702910';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578443';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870162';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101207247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702917';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031536';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100107573';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031537';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101207457';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100132521';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578444';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578445';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043517';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100870180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100078810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702920';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100493188';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100087281';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100217128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578446';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100578447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702956';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100043518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702966';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100031538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100702970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100703028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100703031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA100703032';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:36' WHERE product_id = 'CMA101207509';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207517';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107575';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217129';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087282';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132524';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087283';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870183';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578448';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031539';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043519';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870191';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217130';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493189';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031540';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870197';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870200';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031541';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031542';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087284';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031543';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703038';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870218';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578449';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207528';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870236';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870245';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870254';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703070';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031545';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870266';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870279';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703085';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207573';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207580';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100078974';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703133';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217132';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870300';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870310';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870318';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087285';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870321';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031547';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207605';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107577';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870344';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217133';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870371';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132525';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132527';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132528';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703174';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100078975';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578451';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132535';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132536';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870415';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703180';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703204';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578453';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703237';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703252';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870443';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870457';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703257';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870462';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087286';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578454';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087466';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870465';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870470';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870477';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493191';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207660';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100078976';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031548';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132538';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703266';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703292';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703315';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870480';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870523';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870528';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107579';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870536';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870544';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870548';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217135';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043699';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132541';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107580';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107741';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107742';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493192';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031549';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207673';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087467';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578480';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870550';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100078977';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107744';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087468';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703326';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107745';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207687';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100078978';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217137';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493193';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870577';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043700';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870579';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132545';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870588';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100078980';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578486';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207745';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043701';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578489';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043702';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493195';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132546';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107746';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087469';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870600';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132547';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703339';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703341';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703352';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100703385';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100217138';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100107747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100078981';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870612';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207793';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA101207796';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870619';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870643';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031550';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132550';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100578491';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132551';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132552';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100087471';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031551';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132554';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870656';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100493196';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100132737';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043705';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100043706';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870661';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870665';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100031552';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:37' WHERE product_id = 'CMA100870667';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107751';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031553';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870671';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870681';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132739';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132741';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100493197';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870683';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870686';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100087472';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870691';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100043707';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217139';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107752';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031554';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078982';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078983';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870694';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703387';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217140';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100087473';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870730';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA101207825';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100578593';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100493198';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870734';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078984';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107754';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703402';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107755';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107756';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870748';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA101207849';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100493376';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870751';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132743';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870760';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217141';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100043708';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107759';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA101207870';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870761';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870763';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132745';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107760';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107761';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100578594';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100493377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078985';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107762';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA101207909';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107763';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870768';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031816';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100043709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703410';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703459';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA101207923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703474';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100493378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078986';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217143';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107765';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100087474';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107766';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132746';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100087475';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870781';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870788';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107767';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100578596';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217144';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA101207961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078988';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132747';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100043710';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031817';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100043711';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870792';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031818';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870802';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703477';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100087476';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031819';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031820';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107769';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031821';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870819';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100107770';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078989';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870823';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100493379';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100132749';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA101208008';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100578597';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100043712';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100578598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100703509';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217146';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100578599';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870839';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100087477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100078990';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100493380';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031822';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100031823';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100870969';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:38' WHERE product_id = 'CMA100217147';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578604';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132750';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087478';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493381';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208026';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493382';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087479';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703520';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107772';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043713';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100870991';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217148';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043714';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132752';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208072';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107773';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078992';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871031';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217150';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208117';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078993';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578607';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043715';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107774';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043716';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087480';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132755';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208181';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703540';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578608';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043717';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078994';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100031824';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703564';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100031825';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100031826';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703569';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043718';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703609';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703618';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100031827';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043719';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087481';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578609';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871080';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871102';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078995';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871107';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493385';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217153';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132757';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100031828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703663';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208284';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078996';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208327';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208343';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217154';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043721';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493386';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871119';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578610';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078997';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132758';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208402';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578614';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043722';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703684';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493387';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871151';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703705';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703715';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100031829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703717';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087483';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871178';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217155';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871179';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208441';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208457';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043723';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871193';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107783';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493388';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703727';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871209';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087484';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703729';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871275';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100078999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132759';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100087485';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107786';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871299';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100079001';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208461';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217296';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703778';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100132760';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100578616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703800';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100493389';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107787';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA101208479';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871398';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217297';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871417';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107788';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100217298';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100043724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100703802';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100107789';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100031830';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100079002';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:39' WHERE product_id = 'CMA100871432';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100031831';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100079003';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703823';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100493390';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208563';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087487';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087488';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100043725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871445';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703840';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100031832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703842';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108654';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100493391';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208570';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087489';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703878';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871453';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871459';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871462';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703923';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100578620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703929';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132904';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132905';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108655';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100578621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100079118';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703939';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208584';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217301';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100043726';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208589';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217302';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108656';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871491';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871505';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100031833';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132907';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087490';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100493392';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100578622';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871517';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871529';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208665';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871539';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208683';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100079119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703955';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100703974';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100043879';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871542';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871551';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704005';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704019';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208692';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108657';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208709';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100079120';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132908';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108658';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704020';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871563';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871566';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108659';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132910';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871571';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871587';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871589';
COMMIT;
