BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029775';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104748';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029809';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029821';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757371';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757398';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757462';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029883';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119574';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029897';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757476';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757484';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029927';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757534';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104750';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757650';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029981';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101029996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757688';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119577';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030010';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030015';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757724';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030026';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030048';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757736';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119578';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030093';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757741';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757752';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030172';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757781';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757797';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757808';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757821';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757850';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030340';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030404';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757855';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757885';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119581';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104756';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119582';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119583';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100757978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758006';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030601';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758169';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119746';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030655';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030661';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030663';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104759';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758182';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119748';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030674';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758204';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030703';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758217';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758229';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104762';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758253';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100104763';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030799';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030802';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100119752';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030822';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA101030830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:10' WHERE product_id = 'CMA100758295';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030833';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104764';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030834';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119755';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758372';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758377';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758391';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030867';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758394';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758409';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758423';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758432';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101030992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104767';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758476';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758482';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758486';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758498';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758499';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104768';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031062';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758612';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031073';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031079';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758756';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758800';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104769';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758814';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758816';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104770';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758862';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031340';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031367';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758912';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100119763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100104772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA100758914';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031410';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:11' WHERE product_id = 'CMA101031422';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031432';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031454';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104773';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100758994';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759006';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119766';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759032';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031534';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104775';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031622';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031675';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759146';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759249';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759252';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031753';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031762';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759270';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031782';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031787';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031790';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031803';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031806';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031809';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119770';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119771';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119772';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031830';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759273';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031839';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119773';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031844';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031879';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031883';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759358';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104778';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119774';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031895';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031905';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104779';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119775';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031920';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759361';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759399';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101031980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759400';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759464';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032015';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759504';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032038';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759510';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759522';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759544';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032088';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119911';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032096';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759619';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759622';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759657';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759679';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032146';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032200';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032204';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119912';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032211';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119913';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA101032223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759686';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119914';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100104783';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100759704';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:12' WHERE product_id = 'CMA100119916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759724';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759741';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119918';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032234';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032270';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119919';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759792';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759853';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759854';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119921';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759885';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119923';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032313';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032324';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759945';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119924';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759963';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759981';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100759995';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032455';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032462';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119926';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119927';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032465';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032470';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119928';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760004';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104885';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032494';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760035';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032519';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760111';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760138';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032558';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119932';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032561';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104888';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104889';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032617';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032632';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119935';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104891';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032738';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119936';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119937';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032762';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760208';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032768';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119938';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760249';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032822';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032847';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104893';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760319';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760367';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100119939';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760414';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100120075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760419';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032938';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100120076';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100120077';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101032943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760427';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101033034';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101033035';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101033036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104895';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100120078';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101033053';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100120079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760438';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101033085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100760446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA100104896';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:13' WHERE product_id = 'CMA101033097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760491';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104897';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033142';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033161';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033194';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033199';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033204';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760505';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120081';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760522';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120083';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760543';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033230';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104902';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760559';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033265';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760598';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033308';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033323';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033329';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760620';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760630';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760633';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033388';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033403';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033416';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033434';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760652';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760662';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760672';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033463';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760680';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033498';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760708';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120086';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033517';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033534';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760738';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760749';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033550';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033567';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104906';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760751';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760782';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760810';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760816';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760834';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760853';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760863';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033732';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033749';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760874';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033779';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120089';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760901';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033806';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760919';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760928';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760931';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760974';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033864';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100760983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761014';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761023';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120091';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033927';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104909';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100104910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761089';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101033950';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034023';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100107981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761108';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034054';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761197';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100761223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034167';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034174';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA101034178';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:14' WHERE product_id = 'CMA100120093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761235';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120094';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034218';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034244';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120095';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034267';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034276';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761264';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761274';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107984';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120098';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761289';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120100';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034314';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034346';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034361';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034362';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761294';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761314';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761317';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761321';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107985';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034398';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761337';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034463';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761416';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034505';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034525';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034536';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107986';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107987';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120104';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761497';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107988';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107989';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034598';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034609';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761562';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034610';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034637';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034642';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761633';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034678';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761652';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761702';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034700';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034708';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120109';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034716';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107993';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034739';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107994';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761718';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761755';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761770';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761800';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034825';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034838';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761806';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034842';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761841';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761888';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034877';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034891';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034923';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120112';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761896';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034936';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120114';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107997';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034962';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034971';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761973';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034974';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101034994';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100107999';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035021';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035038';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035062';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035081';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120116';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035092';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035096';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035105';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035171';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035175';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120117';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035180';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120118';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035196';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120119';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035213';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035217';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035220';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035257';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100761989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100762092';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100762099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100108000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100762100';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100120121';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035296';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA101035312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100762115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:15' WHERE product_id = 'CMA100762160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035324';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035326';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120122';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762193';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035342';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035369';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762206';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762230';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035389';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762244';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035419';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035441';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120126';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035461';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762249';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035506';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762288';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762295';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120128';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762296';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762373';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108003';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108004';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035597';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762442';
COMMIT;
