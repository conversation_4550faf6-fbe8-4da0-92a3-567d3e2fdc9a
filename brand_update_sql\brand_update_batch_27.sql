BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035627';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120133';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035647';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035671';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108005';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762552';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120283';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762624';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120284';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762796';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762834';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762848';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108006';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108007';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108009';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035748';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035806';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035811';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035814';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035818';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762891';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762896';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762913';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035823';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035836';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035847';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035854';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035863';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762924';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035904';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762987';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035914';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035932';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100762996';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035938';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763025';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120291';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763028';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763042';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763055';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035996';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763057';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101035999';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120293';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763062';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763107';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763145';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763156';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036097';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036098';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036108';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036134';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120295';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100108218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763180';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100763218';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036204';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036227';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120296';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036234';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120297';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA100120298';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036252';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:16' WHERE product_id = 'CMA101036282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108220';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763420';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763496';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036375';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036382';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120300';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036389';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120301';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763500';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763505';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763531';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036433';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036435';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108222';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036449';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120304';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036468';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763541';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763566';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108225';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763588';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120306';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763596';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763608';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763676';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763693';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763698';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763720';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036682';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036684';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108226';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036697';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763722';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763747';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763750';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763752';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036766';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036767';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120309';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036768';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108227';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036783';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763759';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036815';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763800';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763832';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036829';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036837';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036844';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036858';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036878';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120485';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036879';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036899';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108229';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763843';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763866';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101036990';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037004';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120486';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037033';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037045';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763889';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763920';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763922';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763933';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120488';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037127';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120489';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763939';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037164';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100763998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764013';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764026';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108232';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120492';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120493';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764109';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037171';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037179';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037183';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120495';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120496';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764115';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764121';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120499';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037197';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764143';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108233';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764145';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100108234';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764176';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037275';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764202';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100764205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037308';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037328';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120505';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA100120506';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037346';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037370';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037371';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037382';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037396';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:17' WHERE product_id = 'CMA101037398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108235';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120507';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764217';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037421';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108236';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764287';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037492';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037519';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764298';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037537';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764370';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764373';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764376';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764473';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037627';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764495';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037690';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037713';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764498';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120511';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037717';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764594';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764607';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764608';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037795';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037797';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037799';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037806';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764625';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764634';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120513';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037844';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764657';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037864';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764667';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120699';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764716';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037912';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764727';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037939';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108241';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764795';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764803';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101037999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764816';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120701';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108358';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764846';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038161';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120702';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038197';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038206';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120703';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038216';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100764868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108360';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100120704';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038225';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA101038243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:18' WHERE product_id = 'CMA100108361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764900';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038247';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108363';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038270';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120705';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038279';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108364';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120706';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108365';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038287';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038309';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764914';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764946';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764952';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764954';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764986';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120710';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038344';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100764995';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765004';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120712';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765008';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038419';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765060';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108369';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765068';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765072';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038480';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038491';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108370';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120716';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038524';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120717';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765086';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765096';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765108';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120718';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108371';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765143';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765150';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038637';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120720';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108372';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765169';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765181';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038679';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038681';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038698';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038711';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108373';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108374';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038723';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765195';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765224';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038816';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038831';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038841';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120723';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120724';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038847';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120725';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038854';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038893';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038902';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038924';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100120726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108376';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765355';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100108377';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA100765512';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:19' WHERE product_id = 'CMA101038942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765577';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038948';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038967';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038970';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038972';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038978';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038984';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038989';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101038994';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039001';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039006';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039032';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039036';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765606';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765643';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039065';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039081';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120728';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039083';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765710';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765716';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765731';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765733';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765751';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039131';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039137';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120730';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039140';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039143';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765764';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765784';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039178';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039198';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765795';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039228';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108382';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039264';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039278';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765934';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120732';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765981';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039298';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039309';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100765996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766009';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766055';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766063';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766064';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766092';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766099';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039422';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766165';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039522';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766193';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766201';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766230';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120734';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039560';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039599';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766236';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039613';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039619';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039625';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039643';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039653';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039656';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039658';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766315';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108386';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120736';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039673';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120737';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039679';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766337';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039703';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120738';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766353';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108387';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039735';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039762';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039775';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039788';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766366';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120740';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766503';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766522';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039818';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039843';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039853';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108588';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039873';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039886';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039894';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039903';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039908';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039925';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039939';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039945';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039973';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100108589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766645';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766665';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766676';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766686';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766689';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101039997';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120746';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120747';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100120748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA100766719';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:20' WHERE product_id = 'CMA101040006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108590';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766750';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120749';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766759';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040068';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040093';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040096';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766797';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766839';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766929';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100766935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767032';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767133';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767214';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040150';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040151';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040160';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120751';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767268';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767359';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767372';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767383';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767387';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040195';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767389';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767398';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767401';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040218';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767440';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108593';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120755';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040224';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767441';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040264';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120756';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767459';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040283';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767495';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767507';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767543';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767562';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040367';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040375';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040386';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040399';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040412';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040422';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040424';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040425';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040427';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040431';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767609';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120896';
COMMIT;
