BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366470';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883350';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366506';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883353';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496207';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366510';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366514';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366517';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496348';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366555';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366574';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989221';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366578';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989234';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989252';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366586';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366587';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989277';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989282';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496349';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366588';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989288';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366592';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366596';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366610';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989316';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366614';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883354';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883358';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883360';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883362';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883365';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883368';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883372';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883377';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075160';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075161';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366619';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366629';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366643';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718289';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100081171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718301';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718313';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718322';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088743';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718350';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088744';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718357';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366674';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718397';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075167';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075250';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883378';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075269';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883384';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496352';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989329';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989365';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989373';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088745';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718443';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883394';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075271';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075330';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883421';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883435';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075335';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075361';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883438';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883445';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075370';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046113';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366722';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366731';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883447';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883461';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075383';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883464';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100081172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718472';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075419';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075426';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075430';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046115';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989383';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046117';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075436';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883465';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883560';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883565';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081173';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496354';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075457';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718474';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100046118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718485';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366742';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366749';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075475';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366771';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075492';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366818';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989426';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989430';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718584';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366821';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989461';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989483';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088746';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075502';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075532';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075541';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366828';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366891';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989522';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718586';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718594';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366908';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366917';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718761';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366924';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075548';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075558';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075565';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366937';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366951';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989544';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989548';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989562';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883569';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883574';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883575';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883576';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883579';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883582';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883586';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100046119';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366980';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366987';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081174';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088748';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075577';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075642';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081175';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075666';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101366993';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367001';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718887';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718905';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075676';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075748';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367019';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367032';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367041';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367059';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367065';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883596';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367077';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883611';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367086';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367095';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367116';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989566';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100046120';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075750';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075751';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075753';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989583';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075755';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496355';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367129';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367147';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075760';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075768';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367161';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075774';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075787';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367174';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367176';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367179';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367183';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883616';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496356';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367194';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081176';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075792';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075859';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075863';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883619';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496357';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100046121';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081177';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088751';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883629';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718944';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081179';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496358';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989607';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883639';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883648';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367291';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496359';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081180';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989612';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718965';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081181';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100046122';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367301';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088752';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100718982';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496360';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075924';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883672';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075929';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883676';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075930';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075936';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075939';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367322';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075941';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989636';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496361';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075944';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081182';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075946';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367349';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100046123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100719033';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075954';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496362';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075975';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075978';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883698';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100883700';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101367352';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100496363';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989672';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100989708';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101075982';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA101076010';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100088753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100719094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:14' WHERE product_id = 'CMA100081183';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100046124';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100046125';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101076011';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101076018';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989719';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989738';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883702';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100046126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100081184';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989749';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989756';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496364';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100088754';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367387';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101076031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719134';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883704';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883723';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883725';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367451';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367458';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989809';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989831';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989833';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989837';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719179';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883731';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719189';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100046127';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101076289';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367478';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496365';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100088755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100081185';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883746';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989844';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100088756';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719234';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367483';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367511';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100081186';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101076300';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100046128';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100989850';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883785';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100883789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719252';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367524';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367583';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100081187';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101076313';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496367';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496368';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101076339';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496369';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100046129';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496370';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367599';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367608';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100088757';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367619';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA101367637';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100496371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:15' WHERE product_id = 'CMA100719322';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367641';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076359';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081188';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719343';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719381';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719385';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100989896';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088758';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100883800';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100883811';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081189';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046131';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076392';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076418';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076419';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100989911';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719462';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076425';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076485';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081190';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100883814';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367695';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367706';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367716';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367723';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367728';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367736';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100989919';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367763';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088759';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046132';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367827';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367834';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081192';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496375';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496376';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076513';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367848';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719469';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100989955';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496489';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076525';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367865';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100989994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081193';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367876';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990039';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076563';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101367946';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368048';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076573';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719498';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990157';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046133';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076583';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368057';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368081';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368094';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100883826';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088760';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081194';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046135';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046136';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076610';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368108';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719512';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100883838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719589';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076626';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076638';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496490';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076648';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076661';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076663';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088761';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046137';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088762';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076664';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368127';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368136';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100883846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719598';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076675';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088763';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076679';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081195';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076683';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368151';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046138';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990221';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076707';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496491';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100883853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719619';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719654';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719670';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990257';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368188';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368197';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100081196';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368205';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368215';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368219';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368225';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076724';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076742';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100496492';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990267';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990278';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100088765';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076743';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368233';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990281';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368241';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368267';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368270';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990290';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368279';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990299';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368367';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368375';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368383';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100046139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100719691';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990304';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA100990311';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368394';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368405';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368412';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076769';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101076799';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:16' WHERE product_id = 'CMA101368422';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100088766';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368433';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368458';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368469';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990316';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368483';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990375';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076813';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719778';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719784';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076833';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076892';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883893';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076893';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100081385';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076926';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076931';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496493';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076932';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883905';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883915';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076936';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990399';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368521';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046140';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719796';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368561';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368576';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046141';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076955';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076970';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076974';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719805';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100081386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719865';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883923';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883928';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100081387';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368593';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883939';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883942';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100088767';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496495';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101076990';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496496';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719983';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077006';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077008';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077016';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077018';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077019';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990420';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100088768';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496497';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100081388';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496498';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883954';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883966';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496499';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496500';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990426';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990438';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990446';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100719996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720002';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368623';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720005';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720007';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720011';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496501';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720014';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368763';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368787';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368791';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077042';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368794';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077058';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720024';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496502';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990451';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100081389';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077064';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077070';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077072';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368801';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077076';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368811';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077078';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368820';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368839';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990476';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077080';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046144';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077099';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077109';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720049';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368847';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368860';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368877';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990480';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077113';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077119';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368913';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077124';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077154';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077162';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077169';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077171';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990504';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100081390';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720056';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077201';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046145';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720064';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368948';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101368996';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046147';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369004';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100088769';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720068';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720078';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369026';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077207';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369033';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077212';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720087';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883974';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077214';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077219';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369068';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990507';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369076';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369093';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077223';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883982';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077243';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077246';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720136';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369115';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369117';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990519';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883988';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883994';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883995';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100883997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720142';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990554';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077248';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720173';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720180';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720187';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720196';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720241';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077353';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369183';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369186';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990602';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990635';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990638';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884018';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884025';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100081391';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077371';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100088770';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990670';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100496504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720248';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100990677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720258';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884027';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046148';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046149';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884031';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884040';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077469';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100884045';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100046150';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369189';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077513';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369213';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077520';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077552';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077556';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077559';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720279';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101077580';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369216';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA101369244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:17' WHERE product_id = 'CMA100720341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720351';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720358';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720371';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884049';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100046151';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884068';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369351';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077792';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884077';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088771';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081392';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077796';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077866';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077878';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720379';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369397';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369420';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100496505';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100046152';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990685';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100046336';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100046337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720450';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081394';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101344740';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077884';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720451';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077935';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720500';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720501';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884116';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990739';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720503';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990767';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990799';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101344763';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720552';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884120';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720554';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884141';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884145';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101344808';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101344815';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101344818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720577';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884148';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720632';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884154';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884163';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990884';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100496506';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720633';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884172';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884175';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990930';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884177';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369525';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369546';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884182';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884184';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081396';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100496507';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990974';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101344870';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077942';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101344962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720666';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884185';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884188';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884191';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369558';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369580';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077951';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088773';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077959';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345025';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081397';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100990999';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991004';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720699';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991018';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991038';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101077967';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991043';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078008';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078042';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078048';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345095';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884197';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884205';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884210';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884214';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991051';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991065';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884217';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720773';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991086';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078056';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078058';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078062';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078067';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081398';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720790';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088774';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081399';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100496508';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991116';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720802';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078073';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078189';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991125';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078204';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720863';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369685';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078213';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991130';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720892';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720895';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884231';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884234';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078216';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078219';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078227';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078231';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991141';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078232';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720922';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884239';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991162';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991179';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078239';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078261';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720954';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100720999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721006';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081400';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088775';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345125';
COMMIT;
