BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101394955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100813990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA100814064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:56' WHERE product_id = 'CMA101395378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395913';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101395997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA100814965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:57' WHERE product_id = 'CMA101396192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100814976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396573';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101396987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100815971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA101397568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:58' WHERE product_id = 'CMA100816340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101397989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100816988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817647';
COMMIT;
