/**
 * <PERSON>flare Worker 脚本 - 博客系统 API
 * 
 * 功能：
 * 1. 对 blog 表、author 表、insights 表进行增删改查
 * 2. 管理 insights 和 blog 之间的关联关系
 * 3. 支持 Markdown 文件的上传和下载
 */

// 处理请求的主函数
export default {
  async fetch(request, env) {
    try {
      // 获取数据库和 R2 存储的引用
      const db = env.DB;
      const r2Storage = env['blog-md'];
      
      // 创建路由器
      const router = new Router();
      
      // 博客相关路由
      router.get('/api/blogs', req => handleGetBlogs(req, db));
      router.get('/api/blogs/:id', req => handleGetBlog(req, db));
      router.post('/api/blogs', req => handleCreateBlog(req, db, r2Storage));
      router.put('/api/blogs/:id', req => handleUpdateBlog(req, db, r2Storage));
      router.delete('/api/blogs/:id', req => handleDeleteBlog(req, db, r2Storage));
      
      // 作者相关路由
      router.get('/api/authors', req => handleGetAuthors(req, db));
      router.get('/api/authors/:id', req => handleGetAuthor(req, db));
      router.post('/api/authors', req => handleCreateAuthor(req, db));
      router.put('/api/authors/:id', req => handleUpdateAuthor(req, db));
      router.delete('/api/authors/:id', req => handleDeleteAuthor(req, db));
      
      // 专题相关路由
      router.get('/api/insights', req => handleGetInsights(req, db));
      router.get('/api/insights/:id', req => handleGetInsight(req, db));
      router.post('/api/insights', req => handleCreateInsight(req, db));
      router.put('/api/insights/:id', req => handleUpdateInsight(req, db));
      router.delete('/api/insights/:id', req => handleDeleteInsight(req, db));
      
      // 专题-博客关联关系路由
      router.get('/api/insights/:id/blogs', req => handleGetInsightBlogs(req, db));
      router.post('/api/insights/:id/blogs', req => handleAddBlogToInsight(req, db));
      router.delete('/api/insights/:id/blogs/:blogId', req => handleRemoveBlogFromInsight(req, db));
      
      // Markdown 文件路由
      router.get('/api/markdown/:filename', req => handleGetMarkdown(req, r2Storage));
      router.post('/api/markdown', req => handleUploadMarkdown(req, r2Storage));
      
      // 处理请求
      return router.route(request);
    } catch (err) {
      return new Response(JSON.stringify({ error: err.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
};

// 简单的路由器实现
class Router {
  constructor() {
    this.routes = [];
  }
  
  handle(method, path, handler) {
    this.routes.push({ method, path, handler });
    return this;
  }
  
  get(path, handler) { return this.handle('GET', path, handler); }
  post(path, handler) { return this.handle('POST', path, handler); }
  put(path, handler) { return this.handle('PUT', path, handler); }
  delete(path, handler) { return this.handle('DELETE', path, handler); }
  
  async route(request) {
    const url = new URL(request.url);
    const method = request.method;
    const path = url.pathname;
    
    for (const route of this.routes) {
      const match = this.matchRoute(route.path, path);
      if (match && route.method === method) {
        request.params = match.params;
        request.query = Object.fromEntries(url.searchParams);
        return await route.handler(request);
      }
    }
    
    return new Response('Not Found', { status: 404 });
  }
  
  matchRoute(routePath, requestPath) {
    const routeParts = routePath.split('/');
    const requestParts = requestPath.split('/');
    
    if (routeParts.length !== requestParts.length) {
      return null;
    }
    
    const params = {};
    
    for (let i = 0; i < routeParts.length; i++) {
      if (routeParts[i].startsWith(':')) {
        params[routeParts[i].slice(1)] = requestParts[i];
      } else if (routeParts[i] !== requestParts[i]) {
        return null;
      }
    }
    
    return { params };
  }
}

// 辅助函数 - 解析请求体
async function parseRequestBody(request) {
  const contentType = request.headers.get('Content-Type') || '';
  
  if (contentType.includes('application/json')) {
    return await request.json();
  } else if (contentType.includes('multipart/form-data')) {
    const formData = await request.formData();
    const result = {};
    
    for (const [key, value] of formData.entries()) {
      result[key] = value;
    }
    
    return result;
  } else if (contentType.includes('application/x-www-form-urlencoded')) {
    const formData = await request.formData();
    const result = {};
    
    for (const [key, value] of formData.entries()) {
      result[key] = value;
    }
    
    return result;
  }
  
  return null;
}

// 辅助函数 - 创建 JSON 响应
function jsonResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json' }
  });
}

// 辅助函数 - 生成唯一文件名
function generateUniqueFilename(originalName) {
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop();
  return `${timestamp}-${randomStr}.${extension}`;
}

// ==================== 博客相关处理函数 ====================

// 获取所有博客
async function handleGetBlogs(request, db) {
  const { page = 1, limit = 10, author_id, category, tag } = request.query;
  const offset = (page - 1) * limit;
  
  let query = 'SELECT * FROM blog';
  const params = [];
  const conditions = [];
  
  if (author_id) {
    conditions.push('author_id = ?');
    params.push(author_id);
  }
  
  if (category) {
    conditions.push('category = ?');
    params.push(category);
  }
  
  if (tag) {
    conditions.push('tags LIKE ?');
    params.push(`%${tag}%`);
  }
  
  if (conditions.length > 0) {
    query += ' WHERE ' + conditions.join(' AND ');
  }
  
  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  params.push(parseInt(limit), offset);
  
  const blogs = await db.prepare(query).bind(...params).all();
  const countResult = await db.prepare('SELECT COUNT(*) as total FROM blog').all();
  const total = countResult.results[0].total;
  
  return jsonResponse({
    data: blogs.results,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    }
  });
}

// 获取单个博客
async function handleGetBlog(request, db) {
  const { id } = request.params;
  
  const blog = await db.prepare('SELECT * FROM blog WHERE id = ?').bind(id).first();
  
  if (!blog) {
    return jsonResponse({ error: '博客不存在' }, 404);
  }
  
  // 获取作者信息
  const author = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(blog.author_id).first();
  
  // 获取关联的专题
  const insights = await db.prepare(`
    SELECT i.* FROM insights i
    JOIN insights_blog_map m ON i.id = m.insights_id
    WHERE m.blog_id = ?
  `).bind(id).all();
  
  return jsonResponse({
    ...blog,
    author,
    insights: insights.results
  });
}

// 创建博客
async function handleCreateBlog(request, db, r2Storage) {
  const body = await parseRequestBody(request);
  
  // 验证必填字段
  if (!body.title || !body.author_id) {
    return jsonResponse({ error: '标题和作者ID是必填项' }, 400);
  }
  
  // 检查作者是否存在
  const author = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(body.author_id).first();
  if (!author) {
    return jsonResponse({ error: '作者不存在' }, 400);
  }
  
  // 处理 Markdown 内容
  let markdownUrl = body.content_markdown;
  
  // 如果提供了 Markdown 文件，上传到 R2
  if (body.markdown_file && typeof body.markdown_file !== 'string') {
    const filename = generateUniqueFilename(body.markdown_file.name);
    await r2Storage.put(filename, body.markdown_file);
    markdownUrl = `/api/markdown/${filename}`;
  }
  
  // 插入博客记录
  const result = await db.prepare(`
    INSERT INTO blog (
      type, status, title, subtitle, description, cover_image, 
      content_markdown, tags, category, location, author_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `).bind(
    body.type || 0,
    body.status || 0,
    body.title,
    body.subtitle || null,
    body.description || null,
    body.cover_image || null,
    markdownUrl || null,
    body.tags || null,
    body.category || null,
    body.location || null,
    body.author_id
  ).run();
  
  // 获取新创建的博客
  const newBlog = await db.prepare('SELECT * FROM blog WHERE id = ?').bind(result.meta.last_row_id).first();
  
  return jsonResponse(newBlog, 201);
}

// 更新博客
async function handleUpdateBlog(request, db, r2Storage) {
  const { id } = request.params;
  const body = await parseRequestBody(request);
  
  // 检查博客是否存在
  const blog = await db.prepare('SELECT * FROM blog WHERE id = ?').bind(id).first();
  if (!blog) {
    return jsonResponse({ error: '博客不存在' }, 404);
  }
  
  // 处理 Markdown 内容
  let markdownUrl = body.content_markdown || blog.content_markdown;
  
  // 如果提供了新的 Markdown 文件，上传到 R2
  if (body.markdown_file && typeof body.markdown_file !== 'string') {
    const filename = generateUniqueFilename(body.markdown_file.name);
    await r2Storage.put(filename, body.markdown_file);
    markdownUrl = `/api/markdown/${filename}`;
    
    // 如果有旧的 Markdown 文件，删除它
    if (blog.content_markdown && blog.content_markdown.startsWith('/api/markdown/')) {
      const oldFilename = blog.content_markdown.split('/').pop();
      await r2Storage.delete(oldFilename);
    }
  }
  
  // 更新博客记录
  await db.prepare(`
    UPDATE blog SET
      type = ?,
      status = ?,
      title = ?,
      subtitle = ?,
      description = ?,
      cover_image = ?,
      content_markdown = ?,
      tags = ?,
      category = ?,
      location = ?,
      author_id = ?,
      updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `).bind(
    body.type !== undefined ? body.type : blog.type,
    body.status !== undefined ? body.status : blog.status,
    body.title || blog.title,
    body.subtitle !== undefined ? body.subtitle : blog.subtitle,
    body.description !== undefined ? body.description : blog.description,
    body.cover_image !== undefined ? body.cover_image : blog.cover_image,
    markdownUrl,
    body.tags !== undefined ? body.tags : blog.tags,
    body.category !== undefined ? body.category : blog.category,
    body.location !== undefined ? body.location : blog.location,
    body.author_id || blog.author_id,
    id
  ).run();
  
  // 获取更新后的博客
  const updatedBlog = await db.prepare('SELECT * FROM blog WHERE id = ?').bind(id).first();
  
  return jsonResponse(updatedBlog);
}

// 删除博客
async function handleDeleteBlog(request, db, r2Storage) {
  const { id } = request.params;
  
  // 检查博客是否存在
  const blog = await db.prepare('SELECT * FROM blog WHERE id = ?').bind(id).first();
  if (!blog) {
    return jsonResponse({ error: '博客不存在' }, 404);
  }
  
  // 如果有 Markdown 文件，删除它
  if (blog.content_markdown && blog.content_markdown.startsWith('/api/markdown/')) {
    const filename = blog.content_markdown.split('/').pop();
    await r2Storage.delete(filename);
  }
  
  // 删除博客与专题的关联
  await db.prepare('DELETE FROM insights_blog_map WHERE blog_id = ?').bind(id).run();
  
  // 删除博客
  await db.prepare('DELETE FROM blog WHERE id = ?').bind(id).run();
  
  return jsonResponse({ message: '博客已成功删除' });
}

// ==================== 作者相关处理函数 ====================

// 获取所有作者
async function handleGetAuthors(request, db) {
  const authors = await db.prepare('SELECT * FROM author').all();
  return jsonResponse(authors.results);
}

// 获取单个作者
async function handleGetAuthor(request, db) {
  const { id } = request.params;
  
  const author = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(id).first();
  
  if (!author) {
    return jsonResponse({ error: '作者不存在' }, 404);
  }
  
  // 获取作者的博客
  const blogs = await db.prepare('SELECT * FROM blog WHERE author_id = ? ORDER BY created_at DESC').bind(id).all();
  
  return jsonResponse({
    ...author,
    blogs: blogs.results
  });
}

// 创建作者
async function handleCreateAuthor(request, db) {
  const body = await parseRequestBody(request);
  
  // 验证必填字段
  if (!body.author_id || !body.name) {
    return jsonResponse({ error: '作者ID和姓名是必填项' }, 400);
  }
  
  // 检查作者ID是否已存在
  const existingAuthor = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(body.author_id).first();
  if (existingAuthor) {
    return jsonResponse({ error: '作者ID已存在' }, 400);
  }
  
  // 插入作者记录
  await db.prepare(`
    INSERT INTO author (author_id, name, avatar, description, bio)
    VALUES (?, ?, ?, ?, ?)
  `).bind(
    body.author_id,
    body.name,
    body.avatar || null,
    body.description || null,
    body.bio || null
  ).run();
  
  // 获取新创建的作者
  const newAuthor = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(body.author_id).first();
  
  return jsonResponse(newAuthor, 201);
}

// 更新作者
async function handleUpdateAuthor(request, db) {
  const { id } = request.params;
  const body = await parseRequestBody(request);
  
  // 检查作者是否存在
  const author = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(id).first();
  if (!author) {
    return jsonResponse({ error: '作者不存在' }, 404);
  }
  
  // 更新作者记录
  await db.prepare(`
    UPDATE author SET
      name = ?,
      avatar = ?,
      description = ?,
      bio = ?
    WHERE author_id = ?
  `).bind(
    body.name || author.name,
    body.avatar !== undefined ? body.avatar : author.avatar,
    body.description !== undefined ? body.description : author.description,
    body.bio !== undefined ? body.bio : author.bio,
    id
  ).run();
  
  // 获取更新后的作者
  const updatedAuthor = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(id).first();
  
  return jsonResponse(updatedAuthor);
}

// 删除作者
async function handleDeleteAuthor(request, db) {
  const { id } = request.params;
  
  // 检查作者是否存在
  const author = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(id).first();
  if (!author) {
    return jsonResponse({ error: '作者不存在' }, 404);
  }
  
  // 检查是否有博客引用了该作者
  const blogs = await db.prepare('SELECT COUNT(*) as count FROM blog WHERE author_id = ?').bind(id).first();
  if (blogs.count > 0) {
    return jsonResponse({ error: '无法删除作者，因为有博客引用了该作者' }, 400);
  }
  
  // 删除作者
  await db.prepare('DELETE FROM author WHERE author_id = ?').bind(id).run();
  
  return jsonResponse({ message: '作者已成功删除' });
}

// ==================== 专题相关处理函数 ====================

// 获取所有专题
async function handleGetInsights(request, db) {
  const { page = 1, limit = 10, author_id, tag } = request.query;
  const offset = (page - 1) * limit;
  
  let query = 'SELECT * FROM insights';
  const params = [];
  const conditions = [];
  
  if (author_id) {
    conditions.push('author_id = ?');
    params.push(author_id);
  }
  
  if (tag) {
    conditions.push('tags LIKE ?');
    params.push(`%${tag}%`);
  }
  
  if (conditions.length > 0) {
    query += ' WHERE ' + conditions.join(' AND ');
  }
  
  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  params.push(parseInt(limit), offset);
  
  const insights = await db.prepare(query).bind(...params).all();
  const countResult = await db.prepare('SELECT COUNT(*) as total FROM insights').all();
  const total = countResult.results[0].total;
  
  return jsonResponse({
    data: insights.results,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    }
  });
}

// 获取单个专题
async function handleGetInsight(request, db) {
  const { id } = request.params;
  
  const insight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(id).first();
  
  if (!insight) {
    return jsonResponse({ error: '专题不存在' }, 404);
  }
  
  // 获取作者信息
  const author = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(insight.author_id).first();
  
  // 获取关联的博客
  const blogs = await db.prepare(`
    SELECT b.* FROM blog b
    JOIN insights_blog_map m ON b.id = m.blog_id
    WHERE m.insights_id = ?
    ORDER BY b.created_at DESC
  `).bind(id).all();
  
  return jsonResponse({
    ...insight,
    author,
    blogs: blogs.results
  });
}

// 创建专题
async function handleCreateInsight(request, db) {
  const body = await parseRequestBody(request);
  
  // 验证必填字段
  if (!body.title || !body.author_id) {
    return jsonResponse({ error: '标题和作者ID是必填项' }, 400);
  }
  
  // 检查作者是否存在
  const author = await db.prepare('SELECT * FROM author WHERE author_id = ?').bind(body.author_id).first();
  if (!author) {
    return jsonResponse({ error: '作者不存在' }, 400);
  }
  
  // 插入专题记录
  const result = await db.prepare(`
    INSERT INTO insights (
      type, status, title, subtitle, description, cover_image, 
      tags, author_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `).bind(
    body.type || 0,
    body.status || 0,
    body.title,
    body.subtitle || null,
    body.description || null,
    body.cover_image || null,
    body.tags || null,
    body.author_id
  ).run();
  
  // 获取新创建的专题
  const newInsight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(result.meta.last_row_id).first();
  
  return jsonResponse(newInsight, 201);
}

// 更新专题
async function handleUpdateInsight(request, db) {
  const { id } = request.params;
  const body = await parseRequestBody(request);
  
  // 检查专题是否存在
  const insight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(id).first();
  if (!insight) {
    return jsonResponse({ error: '专题不存在' }, 404);
  }
  
  // 更新专题记录
  await db.prepare(`
    UPDATE insights SET
      type = ?,
      status = ?,
      title = ?,
      subtitle = ?,
      description = ?,
      cover_image = ?,
      tags = ?,
      author_id = ?,
      updated_at = CURRENT_TIMESTAMP
    WHERE id = ?
  `).bind(
    body.type !== undefined ? body.type : insight.type,
    body.status !== undefined ? body.status : insight.status,
    body.title || insight.title,
    body.subtitle !== undefined ? body.subtitle : insight.subtitle,
    body.description !== undefined ? body.description : insight.description,
    body.cover_image !== undefined ? body.cover_image : insight.cover_image,
    body.tags !== undefined ? body.tags : insight.tags,
    body.author_id || insight.author_id,
    id
  ).run();
  
  // 获取更新后的专题
  const updatedInsight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(id).first();
  
  return jsonResponse(updatedInsight);
}

// 删除专题
async function handleDeleteInsight(request, db) {
  const { id } = request.params;
  
  // 检查专题是否存在
  const insight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(id).first();
  if (!insight) {
    return jsonResponse({ error: '专题不存在' }, 404);
  }
  
  // 删除专题与博客的关联
  await db.prepare('DELETE FROM insights_blog_map WHERE insights_id = ?').bind(id).run();
  
  // 删除专题
  await db.prepare('DELETE FROM insights WHERE id = ?').bind(id).run();
  
  return jsonResponse({ message: '专题已成功删除' });
}

// ==================== 专题-博客关联关系处理函数 ====================

// 获取专题关联的博客
async function handleGetInsightBlogs(request, db) {
  const { id } = request.params;
  
  // 检查专题是否存在
  const insight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(id).first();
  if (!insight) {
    return jsonResponse({ error: '专题不存在' }, 404);
  }
  
  // 获取关联的博客
  const blogs = await db.prepare(`
    SELECT b.* FROM blog b
    JOIN insights_blog_map m ON b.id = m.blog_id
    WHERE m.insights_id = ?
    ORDER BY b.created_at DESC
  `).bind(id).all();
  
  return jsonResponse(blogs.results);
}

// 添加博客到专题
async function handleAddBlogToInsight(request, db) {
  const { id } = request.params;
  const body = await parseRequestBody(request);
  
  // 验证必填字段
  if (!body.blog_id) {
    return jsonResponse({ error: '博客ID是必填项' }, 400);
  }
  
  // 检查专题是否存在
  const insight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(id).first();
  if (!insight) {
    return jsonResponse({ error: '专题不存在' }, 404);
  }
  
  // 检查博客是否存在
  const blog = await db.prepare('SELECT * FROM blog WHERE id = ?').bind(body.blog_id).first();
  if (!blog) {
    return jsonResponse({ error: '博客不存在' }, 404);
  }
  
  // 检查关联是否已存在
  const existingMap = await db.prepare(
    'SELECT * FROM insights_blog_map WHERE insights_id = ? AND blog_id = ?'
  ).bind(id, body.blog_id).first();
  
  if (existingMap) {
    return jsonResponse({ error: '该博客已经关联到此专题' }, 400);
  }
  
  // 创建关联
  await db.prepare(
    'INSERT INTO insights_blog_map (insights_id, blog_id) VALUES (?, ?)'
  ).bind(id, body.blog_id).run();
  
  return jsonResponse({ message: '博客已成功添加到专题' });
}

// 从专题中移除博客
async function handleRemoveBlogFromInsight(request, db) {
  const { id, blogId } = request.params;
  
  // 检查专题是否存在
  const insight = await db.prepare('SELECT * FROM insights WHERE id = ?').bind(id).first();
  if (!insight) {
    return jsonResponse({ error: '专题不存在' }, 404);
  }
  
  // 检查博客是否存在
  const blog = await db.prepare('SELECT * FROM blog WHERE id = ?').bind(blogId).first();
  if (!blog) {
    return jsonResponse({ error: '博客不存在' }, 404);
  }
  
  // 检查关联是否存在
  const existingMap = await db.prepare(
    'SELECT * FROM insights_blog_map WHERE insights_id = ? AND blog_id = ?'
  ).bind(id, blogId).first();
  
  if (!existingMap) {
    return jsonResponse({ error: '该博客未关联到此专题' }, 400);
  }
  
  // 删除关联
  await db.prepare(
    'DELETE FROM insights_blog_map WHERE insights_id = ? AND blog_id = ?'
  ).bind(id, blogId).run();
  
  return jsonResponse({ message: '博客已成功从专题中移除' });
}

// ==================== Markdown 文件处理函数 ====================

// 获取 Markdown 文件
async function handleGetMarkdown(request, r2Storage) {
  const { filename } = request.params;
  
  try {
    const object = await r2Storage.get(filename);
    
    if (!object) {
      return jsonResponse({ error: 'Markdown 文件不存在' }, 404);
    }
    
    const headers = new Headers();
    headers.set('Content-Type', 'text/markdown');
    headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    
    return new Response(object.body, {
      headers
    });
  } catch (error) {
    return jsonResponse({ error: '获取 Markdown 文件失败' }, 500);
  }
}

// 上传 Markdown 文件
async function handleUploadMarkdown(request, r2Storage) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!file || !(file instanceof File)) {
      return jsonResponse({ error: '请提供有效的 Markdown 文件' }, 400);
    }
    
    const filename = generateUniqueFilename(file.name);
    await r2Storage.put(filename, file);
    
    return jsonResponse({
      message: 'Markdown 文件上传成功',
      filename,
      url: `/api/markdown/${filename}`
    });
  } catch (error) {
    return jsonResponse({ error: '上传 Markdown 文件失败' }, 500);
  }
}