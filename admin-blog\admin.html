<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>博客管理后台</title>
    <style>
        body { font-family: sans-serif; margin: 0; }
        header { background-color: #f0f0f0; padding: 1em; text-align: center; }
        nav ul { list-style-type: none; padding: 0; margin: 0; display: flex; justify-content: center; background-color: #333; }
        nav ul li { margin: 0; }
        nav ul li a { display: block; color: white; text-align: center; padding: 14px 16px; text-decoration: none; }
        nav ul li a:hover { background-color: #111; }
        .container { padding: 1em; }
        main { padding: 20px; margin-bottom: 60px; /* 为页脚留出空间 */ }
        footer { text-align: center; padding: 1em; background-color: #f0f0f0; position: fixed; bottom: 0; width: 100%;}
        table { width: 100%; border-collapse: collapse; margin-bottom: 1em; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .actions button { margin-right: 5px; }
        .form-group { margin-bottom: 1em; }
        .form-group label { display: block; margin-bottom: .5em; }
        .form-group input, .form-group textarea, .form-group select { width: 100%; padding: .5em; box-sizing: border-box; }
        #authorFormContainer, #blogFormContainer, #insightFormContainer, #markdownUploadFormContainer, #markdownListContainer { margin-top: 20px; padding: 20px; border: 1px solid #ccc; background-color: #f9f9f9;}
        
        /* 错误提示框样式 */
        #api-error-alert {
            display: none;
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            z-index: 1000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            max-width: 80%;
            text-align: center;
        }
        #api-error-alert.show {
            display: block;
        }
        #close-error {
            background: none;
            border: none;
            color: #721c24;
            float: right;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            margin-left: 15px;
        }
    </style>
</head>
<body>
    <!-- 添加API错误提示框 -->
    <div id="api-error-alert">
        <button id="close-error">&times;</button>
        <p id="api-error-message">API请求错误</p>
        <p>可能的解决方案：</p>
        <ol>
            <li>检查API服务器是否运行</li>
            <li>确认API服务器CORS配置</li>
            <li>尝试使用浏览器开发工具诊断</li>
        </ol>
    </div>

    <header>
        <h1>博客管理后台</h1>
    </header>
    <nav>
        <ul>
            <li><a href="#blogs">博客管理</a></li>
            <li><a href="#authors">作者管理</a></li>
            <li><a href="#insights">专题管理</a></li>
            <li><a href="#markdown">Markdown上传</a></li>
        </ul>
    </nav>
    <div class="container">
        <main id="main-content">
            <!-- 内容将在这里根据导航动态加载 -->
            <p>请选择一个管理模块。</p>
        </main>
    </div>
    <footer>
        <p>&copy; 2024 博客管理系统</p>
    </footer>

    <script>
        const API_BASE_URL = 'https://blog-manage.962692556.workers.dev/api'; // 假设的API基础URL
        let allBlogsCache = []; 

        // 显示API错误提示函数
        function showApiError(message) {
            const errorAlert = document.getElementById('api-error-alert');
            const errorMessage = document.getElementById('api-error-message');
            
            errorMessage.textContent = message || 'API请求失败，可能是由于CORS策略阻止。';
            errorAlert.classList.add('show');
            
            // 5秒后自动隐藏
            setTimeout(() => {
                errorAlert.classList.remove('show');
            }, 5000);
        }
        
        // 隐藏API错误提示
        function hideApiError() {
            document.getElementById('api-error-alert').classList.remove('show');
        }
        
        // 页面加载时初始化错误提示框关闭按钮
        window.addEventListener('load', () => {
            document.getElementById('close-error').addEventListener('click', hideApiError);
        });

        async function handleApiResponse(response) {
            // 在no-cors模式下，response.ok始终为false，body为空，状态码为0
            // 检查是否为no-cors响应
            if (response.type === 'opaque') {
                console.log('收到opaque响应，这可能是由于no-cors模式限制。尝试处理...');
                // 对于不透明响应，我们无法读取内容，但可以假定请求成功
                // 因为在开发环境下，我们知道这是CORS问题而不是API错误
                return {}; // 返回空对象，调用方需要处理可能的undefined
            }
            
            if (!response.ok) {
                const errorData = await response.text();
                let errorMessage = `API Error: ${response.status}`;
                try {
                    const jsonData = JSON.parse(errorData);
                    errorMessage += ` - ${jsonData.message || jsonData.error || errorData}`;
                } catch (e) {
                    errorMessage += ` - ${errorData}`;
                }
                throw new Error(errorMessage);
            }
            
            // 对于 DELETE 等可能没有 JSON body 的成功响应
            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
                try {
                    return response.json();
                } catch (error) {
                    console.warn('响应声明为JSON但解析失败:', error);
                    return {};
                }
            }
            
            // 如果不是JSON，尝试返回文本内容，如果失败则返回空对象
            try {
                const text = await response.text();
                return text ? { message: text } : {};
            } catch (error) {
                console.warn('无法读取响应内容:', error);
                return {};
            }
        }

        // 作者管理功能
        async function fetchAuthors() {
            console.log("Fetching authors...");
            const authorsList = document.getElementById('authorsList');
            if (!authorsList) return;
            authorsList.innerHTML = ''; // 清空现有列表

            try {
                const response = await fetch(`${API_BASE_URL}/authors`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const authors = await handleApiResponse(response);
                
                if (!authors || authors.length === 0) {
                    authorsList.innerHTML = '<tr><td colspan="4">No authors found.</td></tr>';
                    console.log("No authors found or empty response.");
                    return;
                }

                authors.forEach(author => {
                    const row = authorsList.insertRow();
                    row.innerHTML = `
                        <td>${author.author_id}</td>
                        <td>${author.name}</td>
                        <td>${author.description || ''}</td>
                        <td class="actions">
                            <button onclick="showEditAuthorForm('${author.author_id}')">编辑</button>
                            <button onclick="deleteAuthor('${author.author_id}')">删除</button>
                        </td>
                    `;
                });
                console.log("Authors displayed.");
            } catch (error) {
                console.error('Error fetching authors:', error);
                showApiError('获取作者列表失败: ' + error.message);
                authorsList.innerHTML = '<tr><td colspan="4">Error loading authors. 可能是CORS问题，请检查API服务器配置。</td></tr>';
            }
        }

        function showAddAuthorForm() {
            document.getElementById('authorFormTitle').textContent = '添加作者';
            document.getElementById('authorForm').reset();
            document.getElementById('authorId').value = ''; 
            document.getElementById('author_id_field').disabled = false;
            document.getElementById('authorFormContainer').style.display = 'block';
        }

        async function showEditAuthorForm(authorId) {
            document.getElementById('authorFormTitle').textContent = '编辑作者';
            document.getElementById('authorForm').reset();
            console.log(`Fetching author ${authorId} for editing...`);
            try {
                const response = await fetch(`${API_BASE_URL}/authors/${authorId}`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const authorData = await handleApiResponse(response);

                document.getElementById('authorId').value = authorData.author_id;
                document.getElementById('author_id_field').value = authorData.author_id;
                document.getElementById('author_id_field').disabled = true;
                document.getElementById('name').value = authorData.name;
                document.getElementById('avatar').value = authorData.avatar || '';
                document.getElementById('description').value = authorData.description || '';
                document.getElementById('bio').value = authorData.bio || '';
                document.getElementById('authorFormContainer').style.display = 'block';
                console.log("Edit form populated for author:", authorId);
            } catch (error) {
                console.error(`Error fetching author ${authorId}:`, error);
                alert(`获取作者信息失败 (ID: ${authorId}): ${error.message}`);
            }
        }

        function hideAuthorForm() {
            document.getElementById('authorFormContainer').style.display = 'none';
            document.getElementById('authorForm').reset();
        }

        async function handleAuthorFormSubmit(event) {
            event.preventDefault();
            const form = event.target;
            const authorIdFromHiddenInput = form.authorId.value; // This is for edit mode
            
            const authorData = {
                name: form.name.value,
                avatar: form.avatar.value,
                description: form.description.value,
                bio: form.bio.value
            };

            let url = `${API_BASE_URL}/authors`;
            let method = 'POST';

            if (authorIdFromHiddenInput) { // Edit mode
                url += `/${authorIdFromHiddenInput}`;
                method = 'PUT';
            } else { // Create mode
                authorData.author_id = form.author_id_field.value; // Add author_id for creation
            }

            console.log(`Submitting author data to ${url} with method ${method}:`, authorData);
            try {
                const response = await fetch(url, {
                    method: method,
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(authorData)
                });
                await handleApiResponse(response);
                alert(`作者 ${method === 'POST' ? '创建' : '更新'}成功!`);
                hideAuthorForm();
                fetchAuthors();
            } catch (error) {
                console.error('Error submitting author form:', error);
                alert(`提交作者信息失败: ${error.message}`);
            }
        }

        async function deleteAuthor(authorId) {
            if (!confirm(`确定要删除作者 ${authorId} 吗？`)) {
                return;
            }
            console.log(`Deleting author ${authorId}...`);
            try {
                const response = await fetch(`${API_BASE_URL}/authors/${authorId}`, { 
                    method: 'DELETE',
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin'
                });
                await handleApiResponse(response);
                alert(`作者 ${authorId} 删除成功!`);
                fetchAuthors();
            } catch (error) {
                console.error(`Error deleting author ${authorId}:`, error);
                alert(`删除作者失败 (ID: ${authorId}): ${error.message}`);
            }
        }

        // 博客管理功能
        async function fetchBlogs() {
            console.log("Fetching blogs...");
            const blogsList = document.getElementById('blogsList');
            if (!blogsList) return;
            blogsList.innerHTML = ''; 

            try {
                const response = await fetch(`${API_BASE_URL}/blogs`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const result = await handleApiResponse(response);
                const blogs = result?.data || []; // 安全地获取data属性，如果result为undefined则使用空数组
                
                if (!blogs || blogs.length === 0) {
                    blogsList.innerHTML = '<tr><td colspan="6">No blogs found.</td></tr>';
                    console.log("No blogs found or empty response.");
                    return;
                }
                const statusMap = {0: '草稿', 1: '已发布', 2: '已归档'};

                blogs.forEach(blog => {
                    const row = blogsList.insertRow();
                    row.innerHTML = `
                        <td>${blog.id}</td>
                        <td>${blog.title}</td>
                        <td>${blog.category || ''}</td>
                        <td>${blog.author_id}</td>
                        <td>${statusMap[blog.status] || '未知'}</td>
                        <td class="actions">
                            <button onclick="showEditBlogForm(${blog.id})">编辑</button>
                            <button onclick="deleteBlog(${blog.id})">删除</button>
                        </td>
                    `;
                });
                console.log("Blogs displayed.");
                allBlogsCache = blogs.map(b => ({id: b.id, title: b.title})); // Update cache
            } catch (error) {
                console.error('Error fetching blogs:', error);
                showApiError('获取博客列表失败: ' + error.message);
                blogsList.innerHTML = '<tr><td colspan="6">Error loading blogs. 可能是CORS问题，请检查API服务器配置。</td></tr>';
            }
        }

        function showAddBlogForm() {
            document.getElementById('blogFormTitle').textContent = '添加博客';
            document.getElementById('blogForm').reset();
            document.getElementById('blogId').value = '';
            document.getElementById('blogFormContainer').style.display = 'block';
        }

        async function showEditBlogForm(blogId) {
            document.getElementById('blogFormTitle').textContent = '编辑博客';
            document.getElementById('blogForm').reset();
            console.log(`Fetching blog ${blogId} for editing...`);
            try {
                const response = await fetch(`${API_BASE_URL}/blogs/${blogId}`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const blogData = await handleApiResponse(response);

                document.getElementById('blogId').value = blogData.id;
                document.getElementById('title').value = blogData.title;
                document.getElementById('subtitle').value = blogData.subtitle || '';
                document.getElementById('description_blog').value = blogData.description || '';
                document.getElementById('cover_image').value = blogData.cover_image || '';
                document.getElementById('content_markdown_url').value = blogData.content_markdown || '';
                // For markdown_file, we don't prefill it as it's a file input
                document.getElementById('tags').value = blogData.tags || '';
                document.getElementById('category').value = blogData.category || '';
                document.getElementById('location').value = blogData.location || '';
                document.getElementById('type').value = blogData.type;
                document.getElementById('status').value = blogData.status;
                document.getElementById('author_id_blog').value = blogData.author_id;
                document.getElementById('blogFormContainer').style.display = 'block';
                console.log("Edit blog form populated for blog:", blogId);
            } catch (error) {
                console.error(`Error fetching blog ${blogId}:`, error);
                alert(`获取博客信息失败 (ID: ${blogId}): ${error.message}`);
            }
        }

        function hideBlogForm() {
            document.getElementById('blogFormContainer').style.display = 'none';
            document.getElementById('blogForm').reset();
        }

        async function handleBlogFormSubmit(event) {
            event.preventDefault();
            const form = document.getElementById('blogForm');
            const blogId = form.blogId.value;
            
            // Create FormData to handle potential file upload
            const formData = new FormData(form); 

            let url = `${API_BASE_URL}/blogs`;
            let method = 'POST';

            if (blogId) { // If blogId is present, it's an update
                url += `/${blogId}`;
                method = 'PUT';
            }
            
            // Remove empty file input if no file is selected, to avoid sending empty part
            const markdownFileInput = form.markdown_file;
            if (markdownFileInput.files.length === 0) {
                 formData.delete('markdown_file');
            }

            console.log(`Submitting blog data to ${url} with method ${method}. FormData prepared.`);
            for (let [key, value] of formData.entries()) {
                console.log(`${key}: ${value instanceof File ? value.name : value }`);
            }

            try {
                const response = await fetch(url, {
                    method: method,
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    body: formData // Let browser set Content-Type for FormData
                });
                await handleApiResponse(response);
                alert(`博客 ${method === 'POST' ? '创建' : '更新'}成功!`);
                hideBlogForm();
                fetchBlogs();
            } catch (error) {
                console.error('Error submitting blog form:', error);
                alert(`提交博客信息失败: ${error.message}`);
            }
        }

        async function deleteBlog(blogId) {
            if (!confirm(`确定要删除博客 ${blogId} 吗？`)) {
                return;
            }
            console.log(`Deleting blog ${blogId}...`);
            try {
                const response = await fetch(`${API_BASE_URL}/blogs/${blogId}`, { 
                    method: 'DELETE',
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin'
                });
                await handleApiResponse(response);
                alert(`博客 ${blogId} 删除成功!`);
                fetchBlogs();
            } catch (error) {
                console.error(`Error deleting blog ${blogId}:`, error);
                alert(`删除博客失败 (ID: ${blogId}): ${error.message}`);
            }
        }

        // 专题管理功能
        async function fetchInsights() {
            console.log("Fetching insights...");
            const insightsList = document.getElementById('insightsList');
            if (!insightsList) return;
            insightsList.innerHTML = ''; 

            try {
                const response = await fetch(`${API_BASE_URL}/insights`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const insights = await handleApiResponse(response);

                if (!insights || insights.length === 0) {
                    insightsList.innerHTML = '<tr><td colspan="4">No insights found.</td></tr>';
                    console.log("No insights found.");
                    return;
                }

                insights.forEach(insight => {
                    const row = insightsList.insertRow();
                    row.innerHTML = `
                        <td>${insight.id}</td>
                        <td>${insight.title}</td>
                        <td>${insight.description || ''}</td>
                        <td class="actions">
                            <button onclick="showEditInsightForm(${insight.id})">编辑</button>
                            <button onclick="deleteInsight(${insight.id})">删除</button>
                            <button onclick="showInsightBlogManager(${insight.id}, '${insight.title.replace(/'/g, "\\\\'")}')">管理博客</button>
                        </td>
                    `;
                });
                console.log("Insights displayed.");
            } catch (error) {
                console.error('Error fetching insights:', error);
                showApiError('获取专题列表失败: ' + error.message);
                insightsList.innerHTML = '<tr><td colspan="4">Error loading insights. 可能是CORS问题，请检查API服务器配置。</td></tr>';
            }
        }

        function showAddInsightForm() {
            document.getElementById('insightFormTitle').textContent = '添加新专题';
            document.getElementById('insightForm').reset();
            document.getElementById('insightId').value = ''; 
            document.getElementById('insightFormContainer').style.display = 'block';
        }

        async function showEditInsightForm(insightId) {
            document.getElementById('insightFormTitle').textContent = '编辑专题';
            document.getElementById('insightForm').reset();
            console.log(`Fetching insight ${insightId} for editing...`);
            try {
                const response = await fetch(`${API_BASE_URL}/insights/${insightId}`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const insightData = await handleApiResponse(response);

                document.getElementById('insightId').value = insightData.id;
                document.getElementById('insight_title').value = insightData.title; 
                document.getElementById('insight_description').value = insightData.description || ''; 
                document.getElementById('insightFormContainer').style.display = 'block';
                console.log("Edit insight form populated for insight:", insightId);
            } catch (error) {
                console.error(`Error fetching insight ${insightId}:`, error);
                alert(`获取专题信息失败 (ID: ${insightId}): ${error.message}`);
            }
        }

        function hideInsightForm() {
            document.getElementById('insightFormContainer').style.display = 'none';
            document.getElementById('insightForm').reset();
        }

        async function handleInsightFormSubmit(event) {
            event.preventDefault();
            const form = document.getElementById('insightForm');
            const insightId = form.insightId.value; 
            const insightData = {
                title: form.insight_title.value,
                description: form.insight_description.value
            };

            let url = `${API_BASE_URL}/insights`; 
            let method = 'POST';

            if (insightId) { 
                url += `/${insightId}`;
                method = 'PUT';
            }

            console.log(`Submitting insight data to ${url} with method ${method}:`, insightData);
            try {
                const response = await fetch(url, {
                    method: method,
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(insightData)
                });
                await handleApiResponse(response);
                alert(`专题 ${method === 'POST' ? '创建' : '更新'}成功!`);
                hideInsightForm();
                fetchInsights(); 
            } catch (error) {
                console.error('Error saving insight:', error);
                alert('保存专题失败: ' + error.message);
            }
        }

        async function deleteInsight(insightId) {
            if (!confirm(`确定要删除专题 ${insightId} 吗？`)) {
                return;
            }
            console.log(`Deleting insight ${insightId}...`);
            try {
                const response = await fetch(`${API_BASE_URL}/insights/${insightId}`, { 
                    method: 'DELETE',
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin'
                });
                await handleApiResponse(response);
                alert(`专题 ${insightId} 删除成功!`);
                fetchInsights(); 
                document.getElementById('insightBlogAssociationManager').style.display = 'none';
            } catch (error) {
                console.error(`Error deleting insight ${insightId}:`, error);
                alert(`删除专题失败 (ID: ${insightId}): ${error.message}`);
            }
        }

        // 专题-博客关联功能
        async function fetchAllBlogsForSelection() {
            if (allBlogsCache.length > 0) {
                console.log("Using cached blogs for selection.");
                return allBlogsCache;
            }
            console.log("Fetching all blogs for selection...");
            try {
                // Fetch a large number, or implement pagination if API supports and UI requires
                const response = await fetch(`${API_BASE_URL}/blogs?limit=1000`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                }); 
                const result = await handleApiResponse(response);
                // api.md for blogs list shows { data: [...], pagination: ... }
                if (result && result.data) {
                    allBlogsCache = result.data.map(blog => ({ id: blog.id, title: blog.title }));
                    console.log("All blogs fetched for selection:", allBlogsCache.length);
                    return allBlogsCache;
                } else {
                     console.warn("fetchAllBlogsForSelection received unexpected response structure:", result);
                     allBlogsCache = []; // Reset cache on error or bad structure
                     return [];
                }
            } catch (error) {
                console.error('Error fetching all blogs for selection:', error);
                alert('获取所有博客列表失败: ' + error.message);
                allBlogsCache = []; // Reset cache on error
                return [];
            }
        }

        async function fetchBlogsForInsight(insightId) {
            console.log(`Fetching blogs for insight ${insightId}...`);
            try {
                const response = await fetch(`${API_BASE_URL}/insights/${insightId}/blogs`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                const blogs = await handleApiResponse(response); // Assuming this API returns an array of blogs directly
                console.log(`Blogs for insight ${insightId}:`, blogs);
                return blogs || [];
            } catch (error) {
                console.error(`Error fetching blogs for insight ${insightId}:`, error);
                alert(`获取专题内博客失败 (专题ID: ${insightId}): ${error.message}`);
                return [];
            }
        }

        async function showInsightBlogManager(insightId, insightTitle) {
            document.getElementById('currentManagingInsightId').value = insightId;
            document.getElementById('managingInsightTitle').textContent = '管理专题: ' + insightTitle;

            const associationManagerDiv = document.getElementById('insightBlogAssociationManager');
            const availableBlogsSelect = document.getElementById('availableBlogsForInsight');
            const currentInsightBlogsListUl = document.getElementById('currentInsightBlogsInList');

            availableBlogsSelect.innerHTML = '';
            currentInsightBlogsListUl.innerHTML = '';
            associationManagerDiv.style.display = 'block'; // Show manager first

            try {
                const allBlogs = await fetchAllBlogsForSelection();
                const insightBlogs = await fetchBlogsForInsight(insightId);
                const insightBlogIds = insightBlogs.map(b => b.id);

                allBlogs.forEach(blog => {
                    if (!insightBlogIds.includes(blog.id)) {
                        const option = document.createElement('option');
                        option.value = blog.id;
                        option.textContent = blog.title;
                        availableBlogsSelect.appendChild(option);
                    }
                });

                insightBlogs.forEach(blog => {
                    const listItem = document.createElement('li');
                    listItem.textContent = `${blog.title} `;
                    const removeButton = document.createElement('button');
                    removeButton.textContent = '移除';
                    removeButton.onclick = () => handleRemoveBlogFromInsight(insightId, blog.id, insightTitle);
                    listItem.appendChild(removeButton);
                    currentInsightBlogsListUl.appendChild(listItem);
                });
            } catch (error) {
                // Errors are handled in fetchAllBlogsForSelection and fetchBlogsForInsight
                // Optionally, display a general error in the manager UI
                currentInsightBlogsListUl.innerHTML = '<li>Error loading blog association data.</li>';
            }
        }

        async function handleAddBlogsToInsight() {
            const insightId = document.getElementById('currentManagingInsightId').value;
            const insightTitle = document.getElementById('managingInsightTitle').textContent.replace('管理专题: ', '');
            const availableBlogsSelect = document.getElementById('availableBlogsForInsight');
            const selectedBlogIds = Array.from(availableBlogsSelect.selectedOptions).map(opt => parseInt(opt.value));

            if (selectedBlogIds.length === 0) {
                alert("请至少选择一个博客添加到专题。");
                return;
            }

            console.log(`Adding blogs ${selectedBlogIds.join(', ')} to insight ${insightId}...`);
            try {
                const response = await fetch(`${API_BASE_URL}/insights/${insightId}/blogs`, {
                    method: 'POST',
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ blog_ids: selectedBlogIds }) // API expects { "blog_ids": [...] }
                });
                await handleApiResponse(response);
                alert(`博客成功添加到专题 "${insightTitle}"!`);
                showInsightBlogManager(parseInt(insightId), insightTitle); 
            } catch (error) {
                console.error(`Error adding blogs to insight ${insightId}:`, error);
                alert(`添加博客到专题失败: ${error.message}`);
            }
        }

        async function handleRemoveBlogFromInsight(insightId, blogId, insightTitle) {
            if (!confirm(`确定要从专题 "${insightTitle}" 中移除博客 (ID: ${blogId}) 吗？`)) return;

            console.log(`Removing blog ${blogId} from insight ${insightId}...`);
            try {
                 // API doc implies DELETE /insights/:id/blogs with body { "blog_ids": [...] }
                const response = await fetch(`${API_BASE_URL}/insights/${insightId}/blogs`, {
                    method: 'DELETE',
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ blog_ids: [blogId] })
                });
                await handleApiResponse(response);
                alert(`博客 (ID: ${blogId}) 已从专题 "${insightTitle}" 移除!`);
                showInsightBlogManager(insightId, insightTitle); 
            } catch (error) {
                console.error(`Error removing blog ${blogId} from insight ${insightId}:`, error);
                alert(`从专题移除博客失败: ${error.message}`);
            }
        }

        // Markdown 文件管理功能
        async function fetchUploadedMarkdowns() {
            console.log("Fetching uploaded markdowns...");
            const markdownsList = document.getElementById('markdownsList');
            if (!markdownsList) return;
            markdownsList.innerHTML = ''; 

            try {
                // Assuming endpoint /api/markdown for listing, adjust if different
                const response = await fetch(`${API_BASE_URL}/markdown`, {
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    headers: {
                        'Accept': 'application/json'
                    }
                }); 
                const markdowns = await handleApiResponse(response);

                if (!markdowns || markdowns.length === 0) {
                    markdownsList.innerHTML = '<tr><td colspan="3">No markdown files found.</td></tr>';
                    console.log("No markdown files found.");
                    return;
                }
                
                // Example structure from mock: { filename: "...", path: "/api/markdown/...", uploaded_at: "..." }
                // `path` should be usable directly in href if it's an absolute path like "/api/markdown/file.md"
                markdowns.forEach(md => {
                    const row = markdownsList.insertRow();
                    // If md.path is a full URL, it's fine. If it's like "/api/markdown/file.md", it's also fine as href.
                    // Avoid API_BASE_URL + md.path if md.path already includes the API prefix.
                    const filePath = md.path; // Assuming md.path is like "/api/markdown/file.md" or full URL.
                    row.innerHTML = `
                        <td>${md.filename}</td>
                        <td><a href="${filePath}" target="_blank">${filePath}</a></td> 
                        <td class="actions">
                            <button onclick="deleteMarkdownFile('${md.filename.replace(/'/g, "\\\\'")}')">删除</button>
                        </td>
                    `;
                });
                console.log("Markdowns displayed.");
            } catch (error) {
                console.error('Error fetching markdowns:', error);
                showApiError('获取Markdown文件列表失败: ' + error.message);
                markdownsList.innerHTML = '<tr><td colspan="3">Error loading markdown files. 可能是CORS问题，请检查API服务器配置。</td></tr>';
            }
        }
        
        function showMarkdownUploadForm(){
            // This function could be used if there's a button to explicitly show the form,
            // but currently it's part of the static layout for #markdown.
            // If the form is initially hidden, this would show it:
            // const formContainer = document.getElementById('markdownUploadFormContainer');
            // if(formContainer) formContainer.style.display = 'block';
        }

        async function handleMarkdownUploadSubmit(event) {
            event.preventDefault();
            const fileInput = document.getElementById('markdownFileToUpload'); // Changed ID for clarity
            const file = fileInput.files[0];

            if (!file) {
                alert("请选择一个Markdown文件。");
                return;
            }

            const formData = new FormData();
            formData.append('markdownFile', file); 

            console.log(`Uploading markdown file ${file.name}...`);
            try {
                const response = await fetch(`${API_BASE_URL}/markdown/upload`, {
                    method: 'POST',
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin',
                    body: formData 
                });
                const result = await handleApiResponse(response); // Expects { path: "...", ... } or similar
                alert('文件上传成功: ' + (result?.path || result?.message || file.name));
                fetchUploadedMarkdowns(); 
                document.getElementById('markdownUploadForm').reset();
            } catch (error) {
                console.error('Error uploading markdown:', error);
                alert('文件上传失败: ' + error.message);
            }
        }

        async function deleteMarkdownFile(filename) {
            if (!confirm(`确定要删除 Markdown 文件 "${filename}" 吗？`)) return;

            console.log(`Deleting markdown file ${filename}...`);
            try {
                const response = await fetch(`${API_BASE_URL}/markdown/${encodeURIComponent(filename)}`, { 
                    method: 'DELETE',
                    mode: 'no-cors', // 添加no-cors模式
                    credentials: 'same-origin'
                });
                await handleApiResponse(response);
                alert(`文件 "${filename}" 删除成功`);
                fetchUploadedMarkdowns(); 
            } catch (error) {
                console.error(`Error deleting markdown ${filename}:`, error);
                alert(`删除文件 "${filename}" 失败: ${error.message}`);
            }
        }

        // 简单的客户端路由，用于显示不同模块的内容
        function loadContent(hash) {
            const mainContent = document.getElementById('main-content');
            mainContent.innerHTML = ''; 
            // 隐藏所有特定于模块的管理界面
            const specificManagers = ['insightBlogAssociationManager', 'authorFormContainer', 'blogFormContainer', 'insightFormContainer', 'markdownUploadFormContainer', 'markdownListContainer'];
            specificManagers.forEach(id => {
                const el = document.getElementById(id);
                if (el) el.style.display = 'none';
            });


            switch (hash) {
                case '#blogs':
                    mainContent.innerHTML = `
                        <h2>博客管理</h2>
                        <button onclick="showAddBlogForm()">添加新博客</button>
                        <div id="blogFormContainer" style="display:none;">
                            <h3 id="blogFormTitle">添加博客</h3>
                            <form id="blogForm" enctype="multipart/form-data">
                                <input type="hidden" id="blogId" name="blogId">
                                <div class="form-group">
                                    <label for="title">标题:</label>
                                    <input type="text" id="title" name="title" required>
                                </div>
                                <div class="form-group">
                                    <label for="subtitle">副标题:</label>
                                    <input type="text" id="subtitle" name="subtitle">
                                </div>
                                <div class="form-group">
                                    <label for="description_blog">描述:</label>
                                    <textarea id="description_blog" name="description_blog"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="cover_image">封面图片 URL:</label>
                                    <input type="url" id="cover_image" name="cover_image">
                                </div>
                                <div class="form-group">
                                    <label for="content_markdown_url">Markdown 内容 URL (可选, 或上传文件):</label>
                                    <input type="url" id="content_markdown_url" name="content_markdown_url">
                                </div>
                                <div class="form-group">
                                    <label for="markdown_file">或上传 Markdown 文件:</label>
                                    <input type="file" id="markdown_file" name="markdown_file" accept=".md,.markdown">
                                </div>
                                <div class="form-group">
                                    <label for="tags">标签 (逗号分隔):</label>
                                    <input type="text" id="tags" name="tags">
                                </div>
                                <div class="form-group">
                                    <label for="category">分类:</label>
                                    <input type="text" id="category" name="category">
                                </div>
                                 <div class="form-group">
                                    <label for="location">位置:</label>
                                    <input type="text" id="location" name="location">
                                </div>
                                <div class="form-group">
                                    <label for="type">类型 (0: Original, 1: Translated, 2: Reprinted):</label>
                                    <select id="type" name="type">
                                        <option value="0">原创</option>
                                        <option value="1">翻译</option>
                                        <option value="2">转载</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="status">状态 (0: Draft, 1: Published, 2: Archived):</label>
                                    <select id="status" name="status">
                                        <option value="0">草稿</option>
                                        <option value="1">已发布</option>
                                        <option value="2">已归档</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="author_id_blog">作者ID:</label>
                                    <input type="text" id="author_id_blog" name="author_id_blog" required>
                                </div>
                                <button type="submit">保存</button>
                                <button type="button" onclick="hideBlogForm()">取消</button>
                            </form>
                        </div>
                        <h3>博客列表</h3>
                        <table id="blogsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>分类</th>
                                    <th>作者ID</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="blogsList">
                            </tbody>
                        </table>
                    `;
                    fetchBlogs(); 
                    document.getElementById('blogForm').addEventListener('submit', handleBlogFormSubmit);
                    // Make blogFormContainer visible if needed, or rely on showAddBlogForm/showEditBlogForm
                    break;
                case '#authors':
                    mainContent.innerHTML = `
                        <h2>作者管理</h2>
                        <button onclick="showAddAuthorForm()">添加新作者</button>
                        <div id="authorFormContainer" style="display:none;">
                            <h3 id="authorFormTitle">添加作者</h3>
                            <form id="authorForm">
                                <input type="hidden" id="authorId" name="authorId">
                                <div class="form-group">
                                    <label for="author_id_field">作者ID (author_id):</label>
                                    <input type="text" id="author_id_field" name="author_id_field" required>
                                </div>
                                <div class="form-group">
                                    <label for="name">名称:</label>
                                    <input type="text" id="name" name="name" required>
                                </div>
                                <div class="form-group">
                                    <label for="avatar">头像 URL:</label>
                                    <input type="url" id="avatar" name="avatar">
                                </div>
                                <div class="form-group">
                                    <label for="description">描述:</label>
                                    <textarea id="description" name="description"></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="bio">简介 (Bio):</label>
                                    <textarea id="bio" name="bio"></textarea>
                                </div>
                                <button type="submit">保存</button>
                                <button type="button" onclick="hideAuthorForm()">取消</button>
                            </form>
                        </div>
                        <h3>作者列表</h3>
                        <table id="authorsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="authorsList">
                            </tbody>
                        </table>
                    `;
                    fetchAuthors(); 
                    document.getElementById('authorForm').addEventListener('submit', handleAuthorFormSubmit);
                    break;
                case '#insights':
                    mainContent.innerHTML = `
                        <h2>专题管理</h2>
                        <button onclick="showAddInsightForm()">添加新专题</button>
                        <div id="insightFormContainer" style="display:none;">
                            <h3 id="insightFormTitle">添加专题</h3>
                            <form id="insightForm">
                                <input type="hidden" id="insightId" name="insightId">
                                <div class="form-group">
                                    <label for="insight_title">专题标题:</label>
                                    <input type="text" id="insight_title" name="insight_title" required>
                                </div>
                                <div class="form-group">
                                    <label for="insight_description">专题描述:</label>
                                    <textarea id="insight_description" name="insight_description"></textarea>
                                </div>
                                <button type="submit">保存</button>
                                <button type="button" onclick="hideInsightForm()">取消</button>
                            </form>
                        </div>
                        <h3>专题列表</h3>
                        <table id="insightsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>标题</th>
                                    <th>描述</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="insightsList">
                            </tbody>
                        </table>
                        <div id="insightBlogAssociationManager" style="display:none;">
                             <h4 id="managingInsightTitle"></h4>
                             <input type="hidden" id="currentManagingInsightId">
                             <div class="form-group">
                                 <label for="availableBlogsForInsight">选择博客添加到专题:</label>
                                 <select id="availableBlogsForInsight" multiple size="5" style="width:100%;"></select>
                             </div>
                             <button onclick="handleAddBlogsToInsight()">添加选中博客到专题</button>
                             <hr>
                             <h5>当前专题内博客:</h5>
                             <ul id="currentInsightBlogsInList" style="list-style-type: disc; padding-left: 20px;"></ul>
                        </div>
                    `;
                    fetchInsights();
                    document.getElementById('insightForm').addEventListener('submit', handleInsightFormSubmit);
                    break;
                case '#markdown':
                    mainContent.innerHTML = `
                        <h2>Markdown上传与管理</h2>
                        <div id="markdownUploadFormContainer">
                            <h3>上传新的 Markdown 文件</h3>
                            <form id="markdownUploadForm">
                                <div class="form-group">
                                    <label for="markdownFileToUpload">选择 Markdown 文件:</label>
                                    <input type="file" id="markdownFileToUpload" name="markdownFile" accept=".md,.markdown" required>
                                </div>
                                <button type="submit">上传文件</button>
                            </form>
                        </div>
                        <div id="markdownListContainer" style="margin-top: 30px;">
                            <h3>已上传的 Markdown 文件列表</h3>
                            <table id="markdownsTable">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>路径/链接</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="markdownsList">
                                    <!-- Markdown files will be listed here -->
                                </tbody>
                            </table>
                        </div>
                    `;
                    fetchUploadedMarkdowns();
                    document.getElementById('markdownUploadForm').addEventListener('submit', handleMarkdownUploadSubmit);
                    // Make containers visible as they are now part of the content for this hash
                    const uploadContainer = document.getElementById('markdownUploadFormContainer');
                    const listContainer = document.getElementById('markdownListContainer');
                    if(uploadContainer) uploadContainer.style.display = 'block';
                    if(listContainer) listContainer.style.display = 'block';
                    break;
                default:
                    mainContent.innerHTML = '<p>请选择一个管理模块。内容区域。Welcome to the Admin Panel. Please select a section from the navigation above to manage content. You can manage blog posts, authors, insights, and markdown files. Each section provides functionalities to add, edit, view, and delete items. For example, in the "Blog Management" section, you can create new blog posts using a form that allows rich text editing or markdown input, categorize them, assign authors, and set their publication status. The "Author Management" section allows you to maintain a list of content creators with their profiles. "Insights Management" is for curating collections of blog posts into specific topics or series. Finally, the "Markdown Upload" section facilitates easy uploading and referencing of markdown files that can be used as content for blog posts. Ensure your backend API is running and accessible at the configured API_BASE_URL for all functionalities to work correctly. Explore the different modules to get familiar with the system. This panel is designed to be intuitive and user-friendly, streamlining your content management workflow. If you encounter any issues or have suggestions for improvement, please refer to the documentation or contact support. Happy managing! This area will dynamically update based on your selection. For instance, if you click on "Blog Management", you will see a list of existing blog posts and an option to add a new one. Similarly, other sections will load their respective interfaces. The system uses JavaScript to fetch and display data from a backend API, providing a seamless single-page application experience. Make sure to check the browser console for any errors if data is not loading as expected. The footer below contains copyright information. The navigation bar at the top provides quick access to all major management modules. Each form for adding or editing content includes relevant fields and validation to ensure data integrity. Action buttons like "Edit" and "Delete" are provided for each item in the lists, allowing for quick modifications. The "Manage Blogs" button in the Insights section allows you to associate specific blog posts with a particular insight or专题. This helps in creating curated content collections. The Markdown upload feature is particularly useful for authors who prefer writing in Markdown and want to easily integrate their files into the blog. The system is built with modern web technologies to ensure a responsive and efficient user experience across different devices. Further enhancements could include user authentication, role-based access control, and more detailed analytics. </p>';
            }
        }

        // 页面加载时根据URL哈希加载内容
        window.addEventListener('load', () => {
            const mainContent = document.getElementById('main-content'); // Ensure mainContent is defined here as well
            if (window.location.hash) {
                loadContent(window.location.hash);
            } else {
                // Default to a welcome message or a specific section
                mainContent.innerHTML = '<p>欢迎来到管理后台。请从上方导航栏中选择一个模块进行管理。例如，您可以管理博客文章、作者、专题以及Markdown文件。每个模块都提供了添加、编辑、查看和删除项目的功能。例如，在"博客管理"模块中，您可以使用表单创建新的博客文章，该表单支持富文本编辑或Markdown输入，对它们进行分类，分配作者，并设置其发布状态。"作者管理"模块允许您维护内容创建者的列表及其个人资料。"专题管理"用于将博客文章集合策划成特定的主题或系列。最后，"Markdown上传"模块方便了Markdown文件的上传和引用，这些文件可用作博客文章的内容。请确保您的后端API正在运行并且可以通过配置的API_BASE_URL访问，以使所有功能正常工作。探索不同的模块以熟悉系统。该面板设计直观且用户友好，可简化您的内容管理工作流程。如果您遇到任何问题或有改进建议，请参阅文档或联系支持。祝您管理愉快！此区域将根据您的选择动态更新。例如，如果您点击"博客管理"，您将看到现有博客文章的列表以及添加新文章的选项。同样，其他部分将加载它们各自的界面。该系统使用JavaScript从后端API获取和显示数据，提供无缝的单页应用程序体验。如果数据未按预期加载，请务必检查浏览器控制台是否有任何错误。下方的页脚包含版权信息。顶部的导航栏可快速访问所有主要管理模块。用于添加或编辑内容的每个表单都包含相关字段和验证，以确保数据完整性。列表中的每个项目都提供了"编辑"和"删除"等操作按钮，以便快速修改。专题部分中的"管理博客"按钮允许您将特定的博客文章与特定的专题关联起来。这有助于创建精选的内容集合。Markdown上传功能对于喜欢用Markdown写作并希望轻松将其文件集成到博客中的作者特别有用。该系统采用现代Web技术构建，以确保在不同设备上提供响应迅速且高效的用户体验。未来的增强功能可能包括用户身份验证、基于角色的访问控制以及更详细的分析数据等等。超过八百字的介绍性内容可以放在这里，详细描述每个模块的功能，使用场景，以及一些最佳实践。例如，在撰写博客时，建议使用清晰的标题和副标题，合理使用标签和分类，以及优化图片大小以提高加载速度。对于作者管理，保持作者信息的最新状态，并提供详细的个人简介，可以增加读者对内容的信任度。专题管理则需要精心策划，确保专题内的文章紧密相关，能够为读者提供系统性的知识。Markdown文件的管理应注意命名规范，方便查找和引用。此外，定期备份数据也是非常重要的，以防意外丢失。本管理后台致力于提供一个高效、便捷的内容管理平台，帮助您更好地运营您的博客。我们也在持续更新和改进系统，未来会加入更多实用的功能，敬请期待。如果您在使用过程中有任何疑问或建议，欢迎随时与我们联系。感谢您的使用！</p>'; 
            }
        });

        // URL哈希改变时加载新内容
        window.addEventListener('hashchange', () => {
            loadContent(window.location.hash);
        });
    </script>
</body>
</html>
