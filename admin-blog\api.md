### 博客相关 API
#### 1. 获取博客列表
- **URL**: `/api/blogs`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码，默认为1
  - `limit`: 每页记录数，默认为10
  - `author_id`: 按作者ID筛选
  - `category`: 按分类筛选
  - `tag`: 按标签筛选（模糊匹配）
- **响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "type": 0,
      "status": 0,
      "title": "博客标题",
      "subtitle": "副标题",
      "description": "描述内容",
      "cover_image": "https://example.com/image.jpg",
      "content_markdown": "/api/markdown/1234567890-abcdef.md",
      "tags": "技术,编程",
      "category": "Web开发",
      "location": "北京",
      "created_at": "2023-05-20T08:00:00Z",
      "updated_at": "2023-05-20T08:00:00Z",
      "author_id": "author123"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```
**CURL 示例**:
```bash
curl -X GET "https://blog-admin.example.com/api/blogs?page=1&limit=10&category=Web开发"
```
#### 2. 获取单个博客
- **URL**: `/api/blogs/:id`
- **方法**: `GET`
- **URL 参数**:
  - `id`: 博客ID
- **响应示例**:
```json
{
  "id": 1,
  "type": 0,
  "status": 0,
  "title": "博客标题",
  "subtitle": "副标题",
  "description": "描述内容",
  "cover_image": "https://example.com/image.jpg",
  "content_markdown": "/api/markdown/1234567890-abcdef.md",
  "tags": "技术,编程",
  "category": "Web开发",
  "location": "北京",
  "created_at": "2023-05-20T08:00:00Z",
  "updated_at": "2023-05-20T08:00:00Z",
  "author_id": "author123",
  "author": {
    "author_id": "author123",
    "name": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "description": "资深开发者",
    "bio": "10年Web开发经验..."
  },
  "insights": [
    {
      "id": 1,
      "title": "前端技术专题",
      "description": "关于前端技术的专题"
    }
  ]
}
```
**CURL 示例**:
```bash
curl -X GET "https://blog-admin.example.com/api/blogs/1"
```
#### 3. 创建博客
- **URL**: `/api/blogs`
- **方法**: `POST`
- **Content-Type**: `application/json` 或 `multipart/form-data`（上传Markdown文件时）
- **请求体**:
```json
{
  "title": "新博客标题",
  "subtitle": "副标题",
  "description": "博客描述",
  "cover_image": "https://example.com/cover.jpg",
  "content_markdown": "https://example.com/content.md",
  "tags": "技术,编程",
  "category": "Web开发",
  "location": "上海",
  "type": 0,
  "status": 0,
  "author_id": "author123"
}
```
- **响应示例**:
```json
{
  "id": 2,
  "type": 0,
  "status": 0,
  "title": "新博客标题",
  "subtitle": "副标题",
  "description": "博客描述",
  "cover_image": "https://example.com/cover.jpg",
  "content_markdown": "https://example.com/content.md",
  "tags": "技术,编程",
  "category": "Web开发",
  "location": "上海",
  "created_at": "2023-05-21T10:00:00Z",
  "updated_at": "2023-05-21T10:00:00Z",
  "author_id": "author123"
}
```
**CURL 示例**:
```bash
curl -X POST "https://blog-admin.example.com/api/blogs" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新博客标题",
    "subtitle": "副标题",
    "description": "博客描述",
    "cover_image": "https://example.com/cover.jpg",
    "tags": "技术,编程",
    "category": "Web开发",
    "author_id": "author123"
  }'
```
**使用 multipart/form-data 上传 Markdown 文件**:
```bash
curl -X POST "https://blog-admin.example.com/api/blogs" \
  -F "title=新博客标题" \
  -F "subtitle=副标题" \
  -F "description=博客描述" \
  -F "author_id=author123" \
  -F "markdown_file=@/path/to/local/file.md"
```
#### 4. 更新博客
- **URL**: `/api/blogs/:id`
- **方法**: `PUT`
- **URL 参数**:
  - `id`: 博客ID
- **Content-Type**: `application/json` 或 `multipart/form-data`（上传Markdown文件时）
- **请求体**:
```json
{
  "title": "更新后的标题",
  "subtitle": "更新后的副标题",
  "description": "更新后的描述",
  "cover_image": "https://example.com/new-cover.jpg",
  "tags": "技术,编程,更新",
  "category": "前端开发",
  "status": 1
}
```
- **响应示例**:
```json
{
  "id": 1,
  "type": 0,
  "status": 1,
  "title": "更新后的标题",
  "subtitle": "更新后的副标题",
  "description": "更新后的描述",
  "cover_image": "https://example.com/new-cover.jpg",
  "content_markdown": "/api/markdown/1234567890-abcdef.md",
  "tags": "技术,编程,更新",
  "category": "前端开发",
  "location": "北京",
  "created_at": "2023-05-20T08:00:00Z",
  "updated_at": "2023-05-21T15:30:00Z",
  "author_id": "author123"
}
```
**CURL 示例**:
```bash
curl -X PUT "https://blog-admin.example.com/api/blogs/1" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的标题",
    "subtitle": "更新后的副标题",
    "description": "更新后的描述",
    "status": 1
  }'
```
#### 5. 删除博客
- **URL**: `/api/blogs/:id`
- **方法**: `DELETE`
- **URL 参数**:
  - `id`: 博客ID
- **响应示例**:
```json
{
  "message": "博客已成功删除"
}
```
**CURL 示例**:
```bash
curl -X DELETE "https://blog-admin.example.com/api/blogs/1"
```