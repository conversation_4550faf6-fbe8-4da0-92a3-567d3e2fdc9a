BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372394';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347241';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347276';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100088972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081620';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885494';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080894';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372467';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347291';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372592';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724487';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347336';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724488';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885504';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885509';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724568';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496619';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496620';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724584';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724598';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885528';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372636';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724665';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347346';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724667';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724727';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080895';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993216';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993244';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080923';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080928';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496622';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100088973';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347361';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347372';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372758';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347376';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347380';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347387';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080932';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724731';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081621';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080948';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724738';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080955';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080973';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885534';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724746';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347391';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347423';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372791';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372811';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081622';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885577';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496623';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100088974';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080978';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080994';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081001';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081003';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372819';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081005';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724789';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081007';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081019';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081020';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347439';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724790';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081025';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347457';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372859';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081035';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347467';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885598';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081057';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081106';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347474';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081121';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347492';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885599';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993253';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993293';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081133';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885602';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081151';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081158';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372899';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372927';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372938';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081160';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372942';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885604';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081181';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081216';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372963';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081222';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081229';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081242';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081244';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372984';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081247';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081264';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885607';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885621';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081328';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081348';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101373017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724827';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347496';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347502';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081623';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100088975';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081353';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081624';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101373024';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101373064';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993303';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101081360';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496820';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100088976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724828';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101373096';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101373117';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885627';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496821';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885633';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885636';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100724897';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373129';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373148';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100081625';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993311';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993355';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993362';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088977';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100724906';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347518';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347524';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885646';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496822';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885659';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100724921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100724944';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885665';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373247';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496824';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885669';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885674';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100724951';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373291';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100724965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100724997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725007';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725008';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347534';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347549';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088978';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347557';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993392';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993407';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496825';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100081626';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373421';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373461';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993462';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725014';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347567';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885679';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885682';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885698';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885706';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373474';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373487';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347575';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885709';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885711';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885714';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885715';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496826';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725019';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885722';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993509';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993529';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081442';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885726';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993550';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081467';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993561';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885731';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885732';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993607';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088979';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885751';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885753';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993611';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993622';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885763';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993629';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081483';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725046';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885786';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725089';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725097';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100081855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993636';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885798';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081485';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725198';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725201';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725210';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088981';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725256';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725264';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993695';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993715';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993727';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100081856';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081511';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088982';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081522';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081523';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496827';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081526';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373533';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725273';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725278';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347582';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347586';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725288';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725295';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725317';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347595';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347610';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347620';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725325';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725338';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088983';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347638';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081556';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081580';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885802';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081583';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081584';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885822';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347647';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081588';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088984';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347658';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100081857';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725450';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725464';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885852';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725466';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373625';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373638';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373652';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885858';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496828';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373691';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373713';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373721';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725472';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081594';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081601';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081603';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373742';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081606';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373783';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725482';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373804';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373811';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885866';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373821';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885878';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993831';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885886';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885894';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885908';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081624';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373825';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725489';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725509';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725516';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885913';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725517';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373877';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725520';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725525';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885922';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885925';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081650';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885929';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725528';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725552';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725577';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885947';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725583';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885951';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885954';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081680';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081688';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081691';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885960';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885964';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725606';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725617';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725620';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725625';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725627';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725640';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725644';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885987';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081727';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081737';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081739';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373932';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081742';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725672';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100885993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725680';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725696';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725705';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373980';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101373994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725738';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100496829';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993895';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100081858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100081859';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088985';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101374015';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347972';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101347976';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100088986';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081746';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081748';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081751';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725739';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886056';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725746';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101374021';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081762';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081783';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081787';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886108';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886114';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993911';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886118';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886120';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886127';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993958';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100993963';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100725803';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081792';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081795';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081800';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081802';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081819';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101081822';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101374027';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA101374047';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886147';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886153';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:24' WHERE product_id = 'CMA100886159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100993967';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886165';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496830';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374050';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374057';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374081';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886196';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081824';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374106';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349468';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081827';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081860';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100088987';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349697';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349718';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374113';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725804';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349758';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374146';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349768';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081861';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725836';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725837';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374211';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725895';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349794';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349801';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081862';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886214';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994005';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374265';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100088988';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496832';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349822';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081837';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886218';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994023';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081918';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886224';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725903';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725911';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725915';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081935';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886249';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081863';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886255';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886261';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349874';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886264';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081937';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886269';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886270';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081944';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886272';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349901';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081952';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081993';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081995';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101081999';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349944';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349959';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886273';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886278';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081864';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886284';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349967';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886288';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994099';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994105';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100088989';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101349995';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082004';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082030';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725935';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082034';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725941';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725945';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374367';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725948';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994133';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496833';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725974';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994216';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994224';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374400';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994226';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350006';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374449';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994243';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374540';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994278';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350063';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374569';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100725980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081865';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350089';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374623';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994282';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994296';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886300';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994300';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726074';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994305';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886318';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350096';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886331';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082126';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350117';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886332';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726087';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994315';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994326';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994331';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726237';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726247';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082168';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082200';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886336';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886337';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350128';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350136';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082201';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726250';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994350';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886347';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100089163';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886351';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082206';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082214';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886355';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082220';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350145';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350153';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082229';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886369';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082238';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726251';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100089164';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350178';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350199';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082244';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350220';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886373';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082257';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886377';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350228';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082260';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886379';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886381';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886383';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886386';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350233';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886390';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082276';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886394';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886396';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886399';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886401';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081866';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886406';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886431';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886440';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350262';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886445';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496835';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886452';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726312';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374662';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726336';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374753';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082285';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496836';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082323';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082327';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082335';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082339';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350267';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994364';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726365';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100089165';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100496837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100081867';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374869';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100994370';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886467';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726466';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101082404';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886473';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350372';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726543';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350383';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726566';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350400';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101374911';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100886502';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA101350402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:25' WHERE product_id = 'CMA100726568';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726585';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994390';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100081868';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726586';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082458';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082486';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082489';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350404';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350408';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350415';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082501';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082532';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100496838';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726647';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726649';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350424';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082571';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350437';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350439';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100089166';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994403';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994416';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100089167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100081869';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375078';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082582';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350444';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886515';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726656';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100496839';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350449';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994432';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726709';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082601';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082603';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082606';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082610';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082613';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082617';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082621';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082626';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726720';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886541';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100994445';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100089168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100726775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100081870';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375089';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886560';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100496840';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375103';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375116';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350455';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375128';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350469';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886568';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350474';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886591';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375188';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350478';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375197';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886594';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350483';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101375199';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886601';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA100886603';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350485';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101082637';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:26' WHERE product_id = 'CMA101350489';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350491';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886608';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350495';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994451';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886618';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082694';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994459';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082723';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375204';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082729';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350500';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375244';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082731';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994464';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100089169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100726782';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100081871';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082735';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375245';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350534';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100089170';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100726787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100726872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100726883';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994468';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994471';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994475';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994478';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100089171';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082753';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082777';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496843';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350584';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100089172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100726901';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994479';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994532';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082782';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994557';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886638';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350588';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350604';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375247';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375268';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100726919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727004';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727011';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994571';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994580';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994585';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994589';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994590';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886681';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886701';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100081872';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350611';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375284';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727023';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100089173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100081873';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994595';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886721';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886736';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082794';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350620';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350628';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727045';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082806';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375294';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496846';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082817';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375340';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375346';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350636';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375354';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375356';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082820';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375359';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082834';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375361';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886753';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100081874';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886761';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082837';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994599';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350641';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727099';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350653';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082847';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082854';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082855';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082857';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350658';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082858';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082863';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375365';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350663';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375603';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350681';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994625';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350688';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994657';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350697';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082894';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727106';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727138';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886839';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886841';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886865';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350701';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350753';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727177';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727183';
COMMIT;
