BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA100837170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:21' WHERE product_id = 'CMA101426888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101426900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101426909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101426926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101426943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101426965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427914';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101427924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100837998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA101428426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:22' WHERE product_id = 'CMA100838067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101428997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838434';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838730';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA100838848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:23' WHERE product_id = 'CMA101429981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101429992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100838989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101430994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA101431149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:24' WHERE product_id = 'CMA100839635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100839982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101431953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101432994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101433007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101433024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA100840555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:25' WHERE product_id = 'CMA101433044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA101433304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:26' WHERE product_id = 'CMA100840719';
COMMIT;
