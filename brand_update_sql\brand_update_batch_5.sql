BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864887';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864896';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399353';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100097086';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864908';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864917';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864919';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864922';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864937';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399354';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202949';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571234';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571235';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086825';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571236';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100097088';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490311';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864938';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864950';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864951';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571237';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202950';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013023';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202951';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864952';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202952';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078195';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013025';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013026';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202953';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013027';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202954';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202955';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042779';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131083';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100097089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078196';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399355';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864980';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100104336';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865010';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078197';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086826';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399362';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399365';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399366';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571238';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865040';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202956';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042780';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131086';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131087';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571239';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202957';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086827';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399425';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202958';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078198';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571242';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399426';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202960';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202961';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013256';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202962';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865056';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865078';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865079';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865086';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202963';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865087';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865144';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571243';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571244';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865152';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865173';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086828';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865177';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086829';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865181';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865183';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399430';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399431';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078199';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131088';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042781';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131089';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131090';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490506';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131092';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865194';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865215';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202964';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100097091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078200';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013257';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042782';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086830';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131094';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490508';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865224';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865228';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865230';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013258';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865232';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865250';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131095';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865255';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865257';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100106046';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013259';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100097092';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865260';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865267';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865270';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202965';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865272';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865286';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013260';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013261';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100696880';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202966';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865332';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865335';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013262';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865340';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202967';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100865343';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571247';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086831';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100696921';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086832';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078201';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571374';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106047';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099322';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131097';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042784';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865346';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865412';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865422';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042785';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865430';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100696974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100696984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100696996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697013';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100202968';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106048';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865432';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865438';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100202969';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078203';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100202970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697019';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571375';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042786';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490510';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865509';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865512';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865514';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865516';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865519';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865522';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865526';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865530';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106049';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078204';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100087026';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571380';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865532';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203186';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100013263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078205';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697028';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042787';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099325';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100013264';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203187';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131263';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865556';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865564';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865571';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106052';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490512';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100013265';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100087027';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203188';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042788';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099326';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571383';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042789';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106054';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865573';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865604';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131265';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697078';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697102';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865681';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100013266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865684';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697162';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131267';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042790';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571385';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571386';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865691';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697228';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099327';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865710';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865718';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203190';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099328';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490513';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106057';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100013267';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865720';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865723';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865724';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490514';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131269';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100087028';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042791';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042792';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697244';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100013268';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100087029';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865735';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099329';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078385';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131270';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042793';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571387';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042794';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203192';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697259';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865739';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131271';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697275';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042795';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099330';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490517';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100042796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078387';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203193';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100106058';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078388';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571390';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571391';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100099331';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100087030';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131273';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100571529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100078389';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490518';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100131274';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100013269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100697406';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100490519';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100043041';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100865743';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:23' WHERE product_id = 'CMA100203195';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106059';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571530';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087031';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106060';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087032';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490520';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865794';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865841';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099332';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490521';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203196';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697432';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131276';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697565';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697575';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078390';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865883';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078391';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697655';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099333';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106061';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571532';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043042';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078392';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099335';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697694';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099336';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697717';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203198';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087034';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013272';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490522';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013273';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697723';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697726';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106063';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490523';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078393';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087035';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865907';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099337';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865912';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865916';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131278';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078394';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043044';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099338';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865918';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106064';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865927';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865929';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865932';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865940';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865946';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697744';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013275';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131279';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697755';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131283';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106156';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043046';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865947';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865950';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697768';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078395';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087036';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131285';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490525';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203200';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571537';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043047';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697774';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865951';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865956';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865961';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106161';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099339';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697798';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697810';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087037';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697811';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865968';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697832';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100865980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866004';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078397';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013276';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490526';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866009';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571540';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203201';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043048';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490527';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106168';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866019';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866022';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078398';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866027';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866045';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131287';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697914';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203202';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131288';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100087038';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100078399';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100490528';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571541';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106169';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099341';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866061';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866074';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866076';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866083';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866088';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866089';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100043049';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866094';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106170';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697931';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866099';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697954';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100697971';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866111';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866114';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106173';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866118';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866119';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100203203';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866122';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866125';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100099342';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100106174';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100571543';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100013278';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100131292';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866129';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:24' WHERE product_id = 'CMA100866134';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106203';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571545';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087039';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100697975';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106204';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100697996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698010';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100078400';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866138';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866155';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043051';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106205';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106206';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571554';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106207';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203204';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043052';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100013279';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100078402';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087040';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100013280';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866160';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866163';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866172';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131295';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698015';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698047';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866181';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866187';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866192';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866203';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100490529';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866205';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698059';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698062';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043053';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571555';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106208';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866216';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866218';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087041';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131297';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100078403';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698076';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203206';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087042';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099344';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866226';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203207';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100490530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698080';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866265';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866275';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866280';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866283';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131300';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866285';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100078404';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866296';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106209';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866300';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866305';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866310';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866312';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203208';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571557';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043054';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131301';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866314';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100013281';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866323';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866331';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131302';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698173';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131307';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100013497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698202';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698215';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698230';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698232';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698276';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698314';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698320';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131308';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131311';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131313';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866418';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100490531';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698326';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203209';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100078405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698330';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131316';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866431';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866433';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866438';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866439';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099346';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106210';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866458';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100490657';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087044';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866474';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203210';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571558';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698356';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100490658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698364';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866475';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866480';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866481';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131317';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100078406';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866489';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100013498';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698368';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866520';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866529';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203211';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131318';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866531';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100203212';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866544';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866553';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866555';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866557';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866565';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571566';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100490659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698372';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087045';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106211';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131473';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866567';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099422';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100210116';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043056';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698391';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698393';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698406';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698442';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099423';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100043057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698480';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866640';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866656';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100087046';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100571568';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866659';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100131474';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100698519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100078407';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100099424';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866676';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100013499';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866680';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100106214';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866691';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100013500';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:25' WHERE product_id = 'CMA100866695';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043058';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866702';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210117';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866716';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866719';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013501';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106217';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131477';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078408';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100571571';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490660';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698532';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106417';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043059';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131478';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100087047';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100099425';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866724';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698535';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698551';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866733';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866734';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698557';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100571572';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866735';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866736';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866737';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131479';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866738';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131480';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131482';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490661';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100571573';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043060';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100099426';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210119';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106418';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866744';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013502';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866753';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866772';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100087048';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866780';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866784';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866787';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043061';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100087049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698592';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866798';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078409';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866803';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043062';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100099427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698601';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210121';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866817';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131484';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698608';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210122';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106419';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078410';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013503';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100573692';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490663';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210123';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866833';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866842';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866851';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866864';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131486';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013504';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013505';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131488';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100099428';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698651';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100087050';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131489';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210124';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698662';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100099429';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100087051';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131492';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698672';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100573693';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043063';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698687';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100573695';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866888';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698699';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866904';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866920';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698706';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490664';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078411';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100573700';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490665';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131493';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131494';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043064';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698752';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131496';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698778';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043065';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100210127';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106423';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100573705';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100087052';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100866989';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698784';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131660';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078412';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698845';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698869';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698872';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867055';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867057';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867058';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867059';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867077';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100867078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078414';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100099430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100078415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100698899';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100490667';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100099431';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100013508';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100043067';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131662';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100087053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100131663';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:26' WHERE product_id = 'CMA100106424';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078416';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210128';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043068';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100087054';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131667';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867080';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867084';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867087';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106425';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106426';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867099';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013509';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100698908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100698921';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210129';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100087055';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210130';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490668';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013510';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867124';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867147';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099432';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131675';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043069';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867150';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867153';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100698937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100698958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078417';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100698965';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078418';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106430';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100698979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699020';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106433';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867160';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043254';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867174';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490669';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699099';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573708';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490671';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099433';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078419';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867188';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210131';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043255';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573853';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100087227';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699183';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490672';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099434';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699195';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013511';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867210';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573854';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490673';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490674';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210132';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013512';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490675';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106434';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490676';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210133';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043256';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867222';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867237';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106436';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867249';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867250';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867252';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867254';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573856';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699220';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490677';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078421';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867256';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210135';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100087228';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013513';
COMMIT;
