import requests
import json

def send_email(api_key, from_email, recipients, subject, html_content):
    """
    使用Resend API发送邮件函数
    
    参数:
        api_key: Resend API密钥
        from_email: 发件人邮箱和名称，例如 "Acme <<EMAIL>>"
        recipients: 收件人列表，例如 ["<EMAIL>"]
        subject: 邮件主题
        html_content: HTML格式的邮件内容
    
    返回:
        响应对象
    """
    url = "https://api.resend.com/emails"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "from": from_email,
        "to": recipients,
        "subject": subject,
        "html": html_content
    }
    
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    return response

def main():
    # 配置信息
    api_key = "re_R6g45kqa_G8QWS5Ta4YgHzYpvCjT9ricc"  # 您的Resend API密钥
    from_email = "service <<EMAIL>>"  # 发件人信息
    recipients = ["<EMAIL>"]  # 收件人邮箱
    subject = "测试邮件[RFQ202506140245]"
    
    # 创建HTML内容 - 询价表格
    html_content = """
    <html>
    <head>
        <style>
            table {
                border-collapse: collapse;
                width: 100%;
            }
            th, td {
                border: 1px solid #dddddd;
                text-align: left;
                padding: 8px;
            }
            th {
                background-color: #f2f2f2;
            }
        </style>
    </head>
    <body>
        <p>尊敬的先生/女士，</p>
        
        <p>希望您一切顺利。请为以下物品提供报价：</p>
        
        <table>
            <tr>
                <th>S. No.</th>
                <th>Item Description</th>
                <th>Quantity</th>
                <th>Unit Price $</th>
                <th>Lead Time</th>
            </tr>
            <tr>
                <td>1</td>
                <td>Micro Commercial Components (MCC) Rectifiers 0.2A 3000V 2100V 3000V 200mA 20A / R3000FGP-TP</td>
                <td>25</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>2</td>
                <td>Rectron Rectifiers 200mA 5000V 500ns/R5000F-B</td>
                <td>25</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>3</td>
                <td>Diotec Semiconductor Rectifiers HV Diode, D7.3x22, 6000V, 1A, 150C/BY6</td>
                <td>25</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>4</td>
                <td>Vishay General Semiconductor Rectifiers Vr/50V Io/1A/1N4001E-E3/54</td>
                <td>25</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>5</td>
                <td>Microchip Technology MOSFET MOSFET SIC 1700 V 750 mOhm TO-247-4/MSC750SMA170B4</td>
                <td>12</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>6</td>
                <td>IXYS MOSFET High Voltage Power MOSFET/IXTH1N450HV</td>
                <td>12</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>7</td>
                <td>IXYS MOSFET MSFT N-CH STD-POLAR3 / IXTH3N200P3HV</td>
                <td>12</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>8</td>
                <td>IXYS MOSFET MSFT N-CH STD-VERY HI VOLTAGE/IXTH02N450HV</td>
                <td>12</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>9</td>
                <td>IXYS MOSFET MSFT N-CH STD-POLAR3 / IXTH1N300P3HV</td>
                <td>12</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>10</td>
                <td>Diodes Incorporated MOSFET P-Ch ENH FET -30V 65mOhm -20V -3.8A/DMP3099L-7</td>
                <td>20</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>11</td>
                <td>Diodes Incorporated Bipolar Transistors - BJT 40V NPN SS Trans 60Vceo 6Vebo 200mA / MMBT3904-7-F</td>
                <td>20</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>12</td>
                <td>Diodes Incorporated MOSFET BSS Family / BSS138-13-F</td>
                <td>20</td>
                <td></td>
                <td></td>
            </tr>
        </table>
        
        <p>谢谢您的配合。</p>
        
        <p>此致，<br>采购部</p>
    </body>
    </html>
    """
    
    # 发送邮件
    response = send_email(api_key, from_email, recipients, subject, html_content)
    
    # 输出结果
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        print("邮件发送成功！")
    else:
        print("邮件发送失败！")

if __name__ == "__main__":
    main()
