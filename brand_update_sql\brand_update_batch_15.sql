BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078297';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100496509';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078300';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884265';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078320';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884269';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884272';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884275';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884283';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369772';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884287';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884323';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078324';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991189';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991191';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884329';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884337';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078378';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721049';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884357';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078391';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884360';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884367';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078419';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884372';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884373';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884383';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884385';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884387';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101369815';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721191';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081401';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991214';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345162';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345249';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345254';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345259';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078450';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088776';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088777';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884398';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345263';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884401';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884402';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884406';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884407';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884408';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721203';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078514';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078552';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345271';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078554';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721205';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100088778';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884411';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100496510';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101345349';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100991247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100081402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721385';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100884413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA100721418';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078559';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078590';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078594';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:18' WHERE product_id = 'CMA101078596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721423';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991256';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345391';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078599';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721460';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345403';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081403';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991319';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991325';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345450';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345469';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369824';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991351';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078671';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991357';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369839';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369847';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991358';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078696';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721470';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369856';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369894';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369900';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081404';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100496511';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721561';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100088779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991415';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991454';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884457';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991459';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991475';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991486';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721577';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078725';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721619';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721633';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721645';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884467';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991512';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991544';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078774';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721710';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884493';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721750';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884499';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884503';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369906';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081405';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884507';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345479';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100088780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721825';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884518';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369927';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369964';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345487';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991562';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991579';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991586';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100496512';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884529';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081406';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991604';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991609';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991623';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078789';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078797';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884555';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991629';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078809';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991667';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345526';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345576';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991683';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884573';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991702';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991708';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078924';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078928';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369974';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991713';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100088781';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081407';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101369999';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370026';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100496513';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370039';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370055';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370066';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100496514';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884578';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991730';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078941';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721886';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370076';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370156';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345608';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078950';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081408';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884579';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078957';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991742';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345622';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101078965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991752';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884597';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884599';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991755';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721957';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884613';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079025';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079033';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991769';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079059';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991783';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345645';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079067';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345660';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079071';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345662';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345664';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100088782';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345672';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079073';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079090';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721976';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370176';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370189';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370200';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370207';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370216';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370233';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100496515';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884621';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884625';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884633';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884636';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884637';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884639';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884648';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079173';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079184';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079195';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370244';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370262';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370272';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370283';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991787';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100088783';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991793';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100496516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100721995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081409';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345677';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079198';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370289';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370305';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370311';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101370315';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991797';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991804';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100722007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100722020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100722025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100722029';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884656';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100088784';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884674';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100081410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100722034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100722147';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100496517';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100991813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101079216';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345684';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345697';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA101345711';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884678';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:19' WHERE product_id = 'CMA100884692';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345714';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345736';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722155';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884705';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345779';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884726';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100081411';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991822';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991834';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991840';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991845';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991847';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370322';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370341';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722202';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722210';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079279';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079281';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079305';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079314';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079327';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884731';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079335';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345807';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884736';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991852';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100496594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100081412';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345835';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722234';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100088785';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370361';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722243';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722257';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991861';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079388';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991877';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991884';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079401';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079402';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100081413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991886';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100088786';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991891';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991895';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100496595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991902';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079409';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345852';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722265';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079483';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345881';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884741';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884750';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100496596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991905';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991939';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991943';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370513';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370524';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100088787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722349';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079484';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884753';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370549';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370562';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079487';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079506';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884762';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884773';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370576';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079509';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370599';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079517';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079520';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079526';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722358';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370613';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722390';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722448';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722456';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079593';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079596';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079599';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079603';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722499';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884778';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884780';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722539';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079621';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079624';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079627';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079632';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079634';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722547';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079637';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884787';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884795';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079642';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079653';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345904';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345908';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100088788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722556';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100496597';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079655';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079676';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722585';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991954';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100081414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722616';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345939';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991959';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991961';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991968';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370658';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884798';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370668';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100496598';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100081597';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345988';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079682';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722628';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722657';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991977';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079700';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079703';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100991994';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079717';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992000';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884823';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370847';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370874';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100884829';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992004';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079719';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370883';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101345996';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101346002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722689';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100496599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722741';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101079735';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA101370896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722747';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992019';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992050';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992061';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100088789';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100088790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100081598';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992065';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100992084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:20' WHERE product_id = 'CMA100722792';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992094';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100088791';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079739';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496600';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884830';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100088792';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101370942';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101370955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722804';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079752';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079760';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079763';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079782';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079792';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884838';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496601';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100081599';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884840';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079795';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079810';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079818';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346078';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079823';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992118';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722819';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079859';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884853';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079944';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101370964';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496602';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079962';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722858';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101079975';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371021';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722904';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722913';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346087';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722918';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722926';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722946';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496606';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884868';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884872';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992156';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992161';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884874';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722958';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722962';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346146';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100081600';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100088793';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722965';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371042';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992176';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100722985';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346171';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371052';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346175';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100081602';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100081603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723000';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992222';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884897';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496608';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080037';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100088960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723010';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080053';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723032';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346192';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080087';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080147';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080164';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080165';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080167';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992258';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496609';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100884906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723035';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371180';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080169';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100081604';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723076';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723079';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371234';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371243';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080256';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371250';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080263';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346228';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101371256';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080277';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346240';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100723083';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346248';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346263';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346274';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346281';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346291';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080282';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100088961';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080320';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101080331';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100496610';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100081605';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA101346374';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:21' WHERE product_id = 'CMA100992341';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884920';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884922';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371267';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884931';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346381';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884934';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100081606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723127';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371291';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371310';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080333';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100081607';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346476';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100496611';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884940';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723151';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992369';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992378';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723157';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371427';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723284';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100081608';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346483';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371445';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100884977';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100496612';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371511';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371521';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346502';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723293';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371549';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346512';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346518';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992404';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371580';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080360';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080394';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080400';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346528';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723301';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723395';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723406';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723444';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723471';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080409';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885009';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100496613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100081610';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723477';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723541';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885029';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346583';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885030';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992552';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723572';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088965';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885048';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885061';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885064';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885067';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371587';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992625';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992629';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992633';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885070';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992641';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992646';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371644';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346605';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885084';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346617';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346641';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346645';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885099';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885140';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346651';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885147';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885173';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346671';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080418';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346676';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992648';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992694';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992698';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992704';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992712';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885189';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346701';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346713';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885197';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992720';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885225';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885238';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885240';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992729';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885242';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885254';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885257';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885267';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885274';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885279';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080467';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080485';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885289';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100081611';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992797';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992800';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371698';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371701';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723622';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371714';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885293';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885298';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885303';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723631';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100496614';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885324';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723691';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723703';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371792';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080510';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723710';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723726';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100081612';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723729';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371862';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723757';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723786';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371899';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723811';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723861';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723867';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723884';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080542';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080546';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080547';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080557';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723890';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100496615';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992906';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723913';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992987';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723985';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100992998';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100993003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723990';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080579';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080598';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080608';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080615';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100993011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100723995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724010';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100993040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100993045';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101346976';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101347035';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885346';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100993052';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371902';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371926';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885358';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100081613';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885389';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885392';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101371937';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100496616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724068';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101372036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724099';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101372047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724100';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101372057';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101347047';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101347056';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100088970';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080629';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885396';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080676';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080680';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080683';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080708';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885405';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080709';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885414';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080710';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724137';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101372097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724189';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100885416';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080717';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080728';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080742';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101080744';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA101372114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:22' WHERE product_id = 'CMA100724236';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724251';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080746';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885446';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080767';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080780';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885451';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080786';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885467';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080795';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724253';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081614';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081616';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724384';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080823';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080836';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081617';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885469';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724418';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885478';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993163';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993165';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100081619';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372182';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347165';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100496618';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080866';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080869';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372208';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080871';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372272';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080873';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080880';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080882';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101080883';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372294';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101372322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724443';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993175';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993181';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100088971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724444';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885491';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885492';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100885493';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA101347224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100724445';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993184';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993195';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:23' WHERE product_id = 'CMA100993212';
COMMIT;
