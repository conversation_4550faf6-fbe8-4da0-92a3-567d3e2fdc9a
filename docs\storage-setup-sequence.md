# 存储服务建立顺序流程

本文档详细说明了创建和配置各种存储服务的推荐顺序，以确保正确建立服务间的关联关系。

## 创建顺序概览

为了确保系统各组件能够正确关联，建议按照以下顺序创建和配置存储服务：

1. 创建 D1 数据库
2. 创建数据库表结构
3. 创建 KV 命名空间
4. 创建 R2 存储桶
5. 创建 Vectorize 索引
6. 配置 wrangler.toml 绑定
7. 初始化基础数据

## 详细步骤

### 1. 创建 D1 数据库

首先创建主数据库，作为系统的核心数据存储：

```bash
# 创建主数据库
wrangler d1 create little-field
```

记录生成的数据库 ID，后续配置 wrangler.toml 时需要使用。

### 2. 创建数据库表结构

按照以下顺序创建数据库表，确保外键约束能够正确建立：

```bash
# 创建 SQL 脚本文件
touch schema.sql
```

在 schema.sql 中按照以下顺序添加表创建语句：

1. 首先创建不依赖其他表的基础表：

```sql
-- 创建品牌表
CREATE TABLE IF NOT EXISTS brands (
    brand_id TEXT PRIMARY KEY,
    full_name TEXT NOT NULL,
    cn_name TEXT,
    en_name TEXT,
    description TEXT,
    category TEXT,
    is_standardized BOOLEAN DEFAULT false
);

-- 创建分销商表
CREATE TABLE IF NOT EXISTS distributors (
    distributor_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    cn_name TEXT,
    en_name TEXT,
    description TEXT,
    category TEXT
);

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    user_id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

2. 然后创建依赖基础表的关联表：

```sql
-- 创建产品表
CREATE TABLE IF NOT EXISTS products (
    product_id TEXT PRIMARY KEY,
    model TEXT NOT NULL,
    brand_id TEXT NOT NULL,
    price_key TEXT,
    stock_key TEXT,
    datasheet_key TEXT,
    image_keys TEXT,
    parameters JSON,
    vector_data JSON,
    description TEXT,
    updated_at TIMESTAMP,
    FOREIGN KEY (brand_id) REFERENCES brands(brand_id)
);

-- 创建分销商产品表
CREATE TABLE IF NOT EXISTS distributor_products (
    product_id TEXT,
    distributor_id TEXT,
    model TEXT NOT NULL,
    brand_id TEXT NOT NULL,
    price_key TEXT,
    stock_key TEXT,
    datasheet_key TEXT,
    image_keys TEXT,
    parameters JSON,
    vector_data JSON,
    description TEXT,
    updated_at TIMESTAMP,
    PRIMARY KEY (product_id, distributor_id),
    FOREIGN KEY (distributor_id) REFERENCES distributors(distributor_id)
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
    order_id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    status TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

3. 最后创建必要的索引：

```sql
-- 创建索引
CREATE INDEX IF NOT EXISTS idx_products_brand_id ON products(brand_id);
CREATE INDEX IF NOT EXISTS idx_distributor_products_distributor_id ON distributor_products(distributor_id);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
```

执行 SQL 脚本创建表结构：

```bash
wrangler d1 execute little-field --file=schema.sql
```

### 3. 创建 KV 命名空间

创建用于存储价格和库存数据的 KV 命名空间：

```bash
# 创建价格数据的 KV 命名空间
wrangler kv:namespace create PRICE_DATA

# 创建库存数据的 KV 命名空间
wrangler kv:namespace create STOCK_DATA
```

记录生成的命名空间 ID，后续配置 wrangler.toml 时需要使用。

### 4. 创建 R2 存储桶

创建用于存储数据表和产品图片的 R2 存储桶：

```bash
# 创建数据表文件的存储桶
wrangler r2 bucket create DATASHEETS

# 创建产品图片的存储桶
wrangler r2 bucket create PRODUCT_IMAGES
```

### 5. 创建 Vectorize 索引

创建用于产品向量搜索的 Vectorize 索引：

```bash
# 创建产品向量索引
wrangler vectorize create products-index \
  --dimensions 1536 \
  --metric cosine
```

记录生成的索引名称，后续配置 wrangler.toml 时需要使用。

### 6. 配置 wrangler.toml 绑定

将所有创建的存储服务绑定到应用中：

```toml
name = "little-field"
main = "src/index.ts"
compatibility_date = "2023-01-01"

# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "little-field"
database_id = "your_database_id"

# KV 命名空间绑定
kv_namespaces = [
  { binding = "PRICE_DATA", id = "your_price_namespace_id" },
  { binding = "STOCK_DATA", id = "your_stock_namespace_id" }
]

# R2 存储桶绑定
r2_buckets = [
  { binding = "DATASHEETS", bucket_name = "datasheets" },
  { binding = "PRODUCT_IMAGES", bucket_name = "product-images" }
]

# Vectorize 索引绑定
vectorize_bindings = [
  { binding = "PRODUCTS_INDEX", index_name = "products-index" }
]
```

### 7. 初始化基础数据

在完成所有存储服务的创建和配置后，按照以下顺序初始化基础数据：

1. 首先插入品牌和分销商数据：

```bash
# 创建初始数据 SQL 脚本
touch init-data.sql
```

```sql
-- 插入品牌数据
INSERT INTO brands (brand_id, full_name, cn_name, en_name, category, is_standardized)
VALUES 
('brand-001', 'Quectel Wireless Solutions', '移远通信', 'Quectel', '通信模块', true),
('brand-002', 'Fibocom Wireless Inc', '广和通', 'Fibocom', '通信模块', true);

-- 插入分销商数据
INSERT INTO distributors (distributor_id, name, cn_name, en_name, category)
VALUES 
('dist-001', 'Digi-Key Electronics', '得捷电子', 'Digi-Key', '电子元器件'),
('dist-002', 'Mouser Electronics', '贸泽电子', 'Mouser', '电子元器件');
```

执行初始化脚本：

```bash
wrangler d1 execute little-field --file=init-data.sql
```

2. 然后创建产品数据，并同步更新相关存储服务：

```javascript
// 示例代码：创建产品并同步到各存储服务
async function createProduct(productData) {
  // 1. 在 D1 数据库中创建产品记录
  const productId = crypto.randomUUID();
  await DB.prepare(
    `INSERT INTO products (product_id, model, brand_id, description, updated_at)
     VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)`
  ).bind(productId, productData.model, productData.brandId, productData.description)
   .run();

  // 2. 在 KV 存储中初始化价格和库存数据
  const priceKey = `product:${productId}:price`;
  const stockKey = `product:${productId}:stock`;
  await PRICE_DATA.put(priceKey, JSON.stringify(productData.price));
  await STOCK_DATA.put(stockKey, JSON.stringify(productData.stock));

  // 3. 更新产品记录中的 KV 键
  await DB.prepare(
    `UPDATE products SET price_key = ?, stock_key = ? WHERE product_id = ?`
  ).bind(priceKey, stockKey, productId).run();

  // 4. 如果有数据表，上传到 R2 存储桶
  if (productData.datasheet) {
    const datasheetKey = `${productId}/spec.pdf`;
    await DATASHEETS.put(datasheetKey, productData.datasheet);
    
    await DB.prepare(
      `UPDATE products SET datasheet_key = ? WHERE product_id = ?`
    ).bind(datasheetKey, productId).run();
  }

  // 5. 如果有产品图片，上传到 R2 存储桶
  if (productData.images) {
    const imageKeys = [];
    for (const [type, image] of Object.entries(productData.images)) {
      const imageKey = `${productId}/${type}.jpg`;
      await PRODUCT_IMAGES.put(imageKey, image);
      imageKeys.push(imageKey);
    }
    
    await DB.prepare(
      `UPDATE products SET image_keys = ? WHERE product_id = ?`
    ).bind(JSON.stringify(imageKeys), productId).run();
  }

  // 6. 生成并存储产品描述向量
  if (productData.description) {
    // 假设已经有一个函数可以生成向量
    const vector = await generateVector(productData.description);
    const vectorId = `product:${productId}`;
    
    await PRODUCTS_INDEX.insert([
      { id: vectorId, values: vector }
    ]);
    
    await DB.prepare(
      `UPDATE products SET vector_data = ? WHERE product_id = ?`
    ).bind(JSON.stringify({ vector_id: vectorId }), productId).run();
  }

  return productId;
}
```

## 依赖关系图

```
D1 数据库
  |
  ├── 品牌表 (brands)
  |     |
  |     └── 产品表 (products) ──┐
  |                             |
  ├── 分销商表 (distributors)   |
  |     |                       |
  |     └── 分销商产品表        |
  |         (distributor_products)
  |                             |
  └── 用户表 (users)            |
        |                       |
        └── 订单表 (orders)     |
                                |
                                ▼
KV 命名空间                      |
  ├── PRICE_DATA ◄──────────────┘
  |   (product:{product_id}:price)
  |                             |
  └── STOCK_DATA ◄──────────────┘
      (product:{product_id}:stock)
                                |
                                ▼
R2 存储桶                        |
  ├── DATASHEETS ◄──────────────┘
  |   ({product_id}/spec.pdf)   |
  |                             |
  └── PRODUCT_IMAGES ◄──────────┘
      ({product_id}/{image_type}.jpg)
                                |
                                ▼
Vectorize 索引                   |
  └── PRODUCTS_INDEX ◄──────────┘
      (product:{product_id})
```

## 最佳实践

1. **遵循创建顺序**：按照本文档推荐的顺序创建和配置存储服务，确保依赖关系正确建立。

2. **使用事务**：在创建或更新跨多个存储服务的数据时，使用事务确保数据一致性。

3. **统一命名规则**：在所有存储服务中使用一致的命名规则，特别是关联键（如 `product_id`）。

4. **备份配置信息**：妥善保存各存储服务的 ID 和配置信息，建议使用版本控制系统管理配置文件。

5. **验证关联关系**：在完成初始设置后，验证各存储服