BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117652';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259314';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117653';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259315';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007243';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259316';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007244';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259317';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259318';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259319';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259320';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606006';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117654';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606007';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018704';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117655';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018705';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259321';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606008';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100484877';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101045725';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117656';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259322';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606009';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259323';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235227';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244319';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606010';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244341';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244350';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104429';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244352';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244356';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244359';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244362';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259324';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100360833';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007346';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606012';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100091427';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283624';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100489289';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283625';
UPDATE products202503 SET brand_id = 'CEB000575', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101256579';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606013';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100011626';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606014';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007347';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007348';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117657';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007349';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100126172';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606015';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606016';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100126173';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283626';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283627';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100126174';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606017';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283628';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283629';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606018';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283630';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283631';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100126175';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283632';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606019';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283633';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007350';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283634';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283635';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606020';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283636';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100126176';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283637';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606021';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606022';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606023';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606024';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259325';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259326';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259327';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259328';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259329';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259330';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259331';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259332';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104434';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104438';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244384';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235236';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104441';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104456';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104457';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104463';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100484882';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007351';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117658';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100126177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100126178';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100044141';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100091428';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259333';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259334';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100485056';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283638';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007352';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283639';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101260433';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283640';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101260438';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235255';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259335';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100485207';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283641';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007353';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606025';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117659';
UPDATE products202503 SET brand_id = 'CEB000399', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101259622';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104465';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101045728';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235262';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235270';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259336';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283642';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606026';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117660';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104469';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045774';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045783';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045793';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045795';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104478';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100126180';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100113729';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100489290';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100489291';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007354';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100091429';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100044142';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100360834';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100283643';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100283644';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100283645';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485208';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018707';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018708';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007355';
UPDATE products202503 SET brand_id = 'CEB002945', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101278296';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260446';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007356';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018709';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018710';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018711';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100086038';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100259337';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018712';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235276';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260478';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260493';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606027';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606028';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606029';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260497';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260505';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260508';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260513';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007357';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606030';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018713';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235283';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235287';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235303';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045800';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104484';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007358';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235312';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235314';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007359';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235319';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007360';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007361';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007362';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104496';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100091430';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100086039';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606031';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100011627';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485209';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100113738';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100044143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100360835';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100489292';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018878';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018879';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260520';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260541';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260550';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260552';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007363';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007364';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007365';
UPDATE products202503 SET brand_id = 'CEB000182', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101281852';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045805';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045823';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045825';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100113739';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104503';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100489293';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100011628';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007366';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007367';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235323';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007368';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007369';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117661';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007370';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100360837';
UPDATE products202503 SET brand_id = 'CEB002621', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101287733';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100091431';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606033';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100126181';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100283646';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100086040';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260558';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117662';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018880';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260567';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606034';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260615';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007371';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007372';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260620';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260626';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260631';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606035';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100044144';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606036';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100044145';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117663';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606037';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606038';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007373';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007374';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606039';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007375';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007551';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018881';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606040';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018882';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606041';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018883';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007552';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007553';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007554';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100259338';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100259339';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100044146';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100259340';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117664';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100259341';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100091432';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018884';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485213';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235359';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485216';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953443';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235391';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485222';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953446';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953451';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953453';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100113740';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260634';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485342';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485344';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606042';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606043';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606044';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953458';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606045';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953470';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606046';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235395';
UPDATE products202503 SET brand_id = 'CEB000046', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101260657';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953480';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606047';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007555';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606048';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235409';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606049';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100126184';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100360839';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485347';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235418';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953518';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235426';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235455';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953530';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007556';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007557';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007558';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007559';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007560';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045827';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045847';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235461';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235476';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485361';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100126185';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100283647';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235490';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100113741';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235499';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235506';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117665';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100283648';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117666';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018885';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007561';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100007562';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117667';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100973613';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100011629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100360840';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117668';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100011630';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100044147';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117669';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485364';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018886';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100283649';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606050';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100606051';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953542';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953555';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018887';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100011631';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953557';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287110';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287111';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287112';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104513';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045849';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045868';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104523';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104535';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953566';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235509';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235521';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287113';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100973937';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100974093';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287114';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100974172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100485376';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117671';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117672';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287116';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287117';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100126186';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117673';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100287118';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117674';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117675';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018888';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018889';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100086041';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018890';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953587';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018891';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018892';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117676';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235527';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235536';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101235542';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045879';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045893';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045913';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101104543';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100018893';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100117677';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953592';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100489294';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045914';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953623';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101045933';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953660';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953742';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953762';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953777';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953787';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100953791';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100113885';
UPDATE products202503 SET brand_id = 'CEB002610', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA101289938';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:58' WHERE product_id = 'CMA100011632';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100086042';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489295';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289640';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289647';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100360841';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100091433';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126188';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117679';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289658';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100044148';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011633';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289666';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289672';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289676';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289677';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126189';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018894';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018895';
UPDATE products202503 SET brand_id = 'CEB003169', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289752';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100953792';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100953793';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100953819';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018896';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018897';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235544';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018898';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100953847';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100485379';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289681';
UPDATE products202503 SET brand_id = 'CEB003084', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101289688';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100044149';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100113892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100360845';
UPDATE products202503 SET brand_id = 'CEB002610', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101290041';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606052';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126190';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018899';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287119';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606054';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489296';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100086043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126192';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235562';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117680';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011634';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606055';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117681';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117682';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606056';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117683';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606057';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100091434';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606058';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018900';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606059';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606060';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117684';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104559';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117685';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018901';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606061';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100606062';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101045936';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101045942';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610540';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610541';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117686';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018902';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610542';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610543';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610544';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610545';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610546';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018903';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018904';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610547';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610548';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018905';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610549';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101045954';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287120';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610550';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100086044';
UPDATE products202503 SET brand_id = 'CEB002342', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294037';
UPDATE products202503 SET brand_id = 'CEB002342', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294059';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100360846';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235586';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235607';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235611';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235616';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610551';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235619';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126193';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610552';
UPDATE products202503 SET brand_id = 'CEB002348', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294116';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235620';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610553';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235623';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610554';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018906';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117687';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235626';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126194';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100044150';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287121';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287122';
UPDATE products202503 SET brand_id = 'CEB000135', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101290915';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018907';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018908';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018909';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287123';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287124';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610555';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610556';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100485385';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018910';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100485386';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489297';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610557';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018911';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610558';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610559';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018912';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287125';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235639';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235646';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610560';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100113894';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610561';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235653';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235664';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100485387';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018913';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018914';
UPDATE products202503 SET brand_id = 'CEB000135', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101290916';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104572';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294678';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100485389';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610562';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117688';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100044314';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126395';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100113896';
UPDATE products202503 SET brand_id = 'CEB000240', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294814';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100091436';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287126';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117689';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018915';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018916';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610563';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018917';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018918';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011636';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018919';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011637';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294696';
UPDATE products202503 SET brand_id = 'CEB001281', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294709';
UPDATE products202503 SET brand_id = 'CEB002155', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294738';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294706';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018920';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294727';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294735';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294742';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489298';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018921';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117690';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018922';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235674';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294751';
UPDATE products202503 SET brand_id = 'CEB000241', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294831';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104586';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610564';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610565';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100360991';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610566';
UPDATE products202503 SET brand_id = 'CEB002411', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101295269';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610567';
UPDATE products202503 SET brand_id = 'CEB002411', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101295272';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235703';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100485399';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489890';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100091437';
UPDATE products202503 SET brand_id = 'CEB001428', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101295201';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610568';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100113897';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011638';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100044315';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610569';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489299';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101045960';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101045987';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101045994';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104589';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104591';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104595';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101045995';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101235718';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287127';
UPDATE products202503 SET brand_id = 'CEB000249', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101295295';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287128';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287129';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610570';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287130';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100044316';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100126396';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489893';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100086045';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100091547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100361002';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294753';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489894';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294756';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011639';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100011640';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610571';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117691';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117692';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100287131';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489300';
UPDATE products202503 SET brand_id = 'CEB002580', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101298617';
UPDATE products202503 SET brand_id = 'CEB002580', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101298625';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489895';
UPDATE products202503 SET brand_id = 'CEB003066', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101301385';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018923';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018924';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018925';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100018926';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610572';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100117693';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610573';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100121860';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100113901';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294763';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100870425';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294771';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100870436';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101294781';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100870442';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610574';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100610575';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA100489896';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101046073';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101046084';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104604';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104613';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101046093';
UPDATE products202503 SET brand_id = 'CEB000865', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101303916';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104618';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101104623';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:59' WHERE product_id = 'CMA101046102';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489301';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100086046';
UPDATE products202503 SET brand_id = 'CEB002126', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101305737';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018927';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100361007';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018929';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018930';
UPDATE products202503 SET brand_id = 'CEB002126', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101305742';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018931';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226527';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018932';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100113903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100361008';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226593';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100870448';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100870518';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100870541';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100091548';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100870543';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018933';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018934';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287132';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018935';
UPDATE products202503 SET brand_id = 'CEB000999', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101307158';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101235733';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226699';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226707';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101235745';
UPDATE products202503 SET brand_id = 'CEB000865', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101303925';
UPDATE products202503 SET brand_id = 'CEB000865', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101303931';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104628';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046122';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104630';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104634';
UPDATE products202503 SET brand_id = 'CEB003173', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101309157';
UPDATE products202503 SET brand_id = 'CEB003173', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101309158';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104651';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104659';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610576';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610577';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100011641';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610578';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489909';
UPDATE products202503 SET brand_id = 'CEB000924', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313418';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100114087';
UPDATE products202503 SET brand_id = 'CEB001488', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313369';
UPDATE products202503 SET brand_id = 'CEB003309', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313559';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100018936';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287133';
UPDATE products202503 SET brand_id = 'CEB002025', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101309239';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610579';
UPDATE products202503 SET brand_id = 'CEB001488', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126398';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287134';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100091549';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101294792';
UPDATE products202503 SET brand_id = 'CEB001488', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313394';
UPDATE products202503 SET brand_id = 'CEB003288', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101294796';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226730';
UPDATE products202503 SET brand_id = 'CEB001488', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313402';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100361009';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126400';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126401';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610581';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610582';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610583';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489302';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610584';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126403';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610585';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126404';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104665';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610586';
UPDATE products202503 SET brand_id = 'CEB001488', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313410';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610587';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610588';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126406';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610589';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100086047';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610590';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126407';
UPDATE products202503 SET brand_id = 'CEB001488', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313425';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610591';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610592';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046136';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610593';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610594';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610595';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046149';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046189';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100114089';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104675';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046204';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019091';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046214';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226739';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046225';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226750';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100102296';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287135';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287136';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489478';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287137';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100024883';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287138';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100077477';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287140';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019092';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126408';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019093';
UPDATE products202503 SET brand_id = 'CEB001652', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313578';
UPDATE products202503 SET brand_id = 'CEB003197', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101313653';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287142';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019094';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019095';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489911';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226753';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610596';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100086048';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019096';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019097';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100114221';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019098';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100091550';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019099';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100011642';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100361010';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610597';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104681';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104691';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046231';
UPDATE products202503 SET brand_id = 'CEB003279', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101311035';
UPDATE products202503 SET brand_id = 'CEB003279', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101311039';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046232';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100361012';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287143';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100490177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126409';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100102297';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287144';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100199820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100077478';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100024884';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489479';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489480';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019101';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019102';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610598';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610599';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100114224';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610600';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019103';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100533042';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019104';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019105';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610601';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610602';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489481';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104694';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046269';
UPDATE products202503 SET brand_id = 'CEB003279', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101311048';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100610603';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019106';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891123';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100490188';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100011852';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100490194';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019107';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046308';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046313';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046320';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046327';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046331';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100102298';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100011853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100361013';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226789';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891237';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126413';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226793';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226796';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891241';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100490396';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100011854';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100091551';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100086049';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287145';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100086050';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100533043';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891257';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891283';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489482';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104700';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104711';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104720';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046334';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104725';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046339';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101104727';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891284';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046346';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100077479';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046353';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100114225';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100126414';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100102299';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100091552';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226799';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226860';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100287146';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226868';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489483';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100199821';
UPDATE products202503 SET brand_id = 'CEB003029', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101732802';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019108';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019109';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019110';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019111';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100490398';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891310';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891327';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100361014';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891363';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226877';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226883';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100024885';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019112';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019113';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891418';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891432';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101226902';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA101046416';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019114';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100019115';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100891450';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100489484';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100114226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:00' WHERE product_id = 'CMA100077480';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100024886';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100126417';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019116';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019117';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100091553';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019118';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019219';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490399';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100891461';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100891679';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533044';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100891792';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533045';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100891971';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100011855';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287147';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533046';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287148';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100892042';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100102300';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100086051';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100011856';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019220';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287150';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100361016';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019221';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046434';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287153';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019222';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287154';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287155';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104730';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104737';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019223';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019224';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287157';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287159';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100287161';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019225';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290364';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046448';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046461';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046462';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290365';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046478';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290366';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046517';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290367';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100086241';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046533';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100024887';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100086242';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100489485';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490400';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100126418';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490403';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100199822';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100011857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100077481';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100362337';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100102301';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104746';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533047';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104763';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100011858';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290370';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490408';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100199823';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019226';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100114227';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100489486';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046558';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100102302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100126419';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100024888';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100091554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100126420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100077482';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490410';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104772';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104781';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490566';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019227';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019228';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019229';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019230';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104786';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104793';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104798';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019231';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019232';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100102304';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019233';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104803';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490567';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100011859';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100114424';
COMMIT;
