BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090384';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360059';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090387';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360075';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733782';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733815';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101080640';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360094';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100082258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733824';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733840';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100089566';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733843';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385803';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101083380';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890838';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385910';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733860';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733875';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090402';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090410';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090411';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890847';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090412';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890851';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733877';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000364';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000426';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385975';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733885';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733895';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090433';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385998';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101386040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000437';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000440';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000441';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101386204';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100089567';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090447';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090451';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100082259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000454';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000461';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000475';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000477';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101386278';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101386304';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000504';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000509';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360116';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101386345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000512';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000516';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000524';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101084125';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000531';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000546';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733948';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733963';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000559';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733987';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000569';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000583';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733989';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734061';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734063';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090502';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090505';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090508';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734066';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734073';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090515';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100734093';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090521';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090538';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090547';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090554';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090573';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000601';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000605';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734096';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101087274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100082260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734121';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360141';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734124';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089846';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100089568';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090598';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734158';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734167';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000611';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386373';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734171';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386392';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000612';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734195';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734198';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734204';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734214';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360194';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089868';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089894';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000753';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000760';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089961';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089963';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360202';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089966';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360208';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734254';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386517';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000770';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000780';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000783';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360240';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089981';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090629';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100089569';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101089995';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090007';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090654';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090052';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386555';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090065';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090664';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100890898';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386560';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090081';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100890936';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090686';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090083';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090695';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090089';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360269';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090090';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360289';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734275';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734306';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090124';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100890964';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090138';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100890974';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090152';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100890979';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100082261';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734317';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386652';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090178';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090189';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090699';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090198';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000793';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100890983';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090228';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090703';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090711';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386706';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100890998';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090238';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734327';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360306';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100082334';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360340';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100089570';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360350';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734418';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734444';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734453';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360378';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734456';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090745';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090751';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000841';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734467';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386730';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386753';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360392';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090246';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090263';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090756';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090770';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360406';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090264';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360407';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386764';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891029';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090282';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734480';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000929';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734521';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891035';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734611';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891064';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000979';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891077';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734632';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101000990';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090925';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090928';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090931';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891083';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360426';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090287';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090305';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090932';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090311';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734635';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100089571';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090325';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090327';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090935';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090355';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090938';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090357';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360462';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734640';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386790';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734674';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734677';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090449';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734679';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100082335';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090507';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001022';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090509';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734694';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090535';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734708';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386860';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090558';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734710';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090655';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734731';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101386924';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387001';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891124';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734748';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360465';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360474';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360491';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387085';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090670';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090684';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090692';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360512';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090739';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891142';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090742';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090754';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387110';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090768';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090774';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360524';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090778';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090981';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090803';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090989';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734769';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734772';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360555';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090806';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090810';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090818';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091024';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090819';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091044';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001033';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001074';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001087';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001095';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734803';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360572';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360593';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360607';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090825';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360611';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090836';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091086';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734822';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091105';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734838';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734845';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734850';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734859';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091137';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090840';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090864';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090880';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360642';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101090893';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091011';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091022';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091033';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360660';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734864';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091046';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360669';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360677';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091102';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091124';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001099';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091130';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360696';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001129';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360712';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001137';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001162';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001199';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387175';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387193';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891149';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891187';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360727';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100734890';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891198';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100089572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735013';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891199';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735034';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100082336';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735060';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735078';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387239';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360752';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001200';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001216';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387242';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100082337';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360771';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091142';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091149';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091155';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091164';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735084';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735108';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360809';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100089573';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735109';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891234';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387252';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001235';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101387275';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001257';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891255';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091171';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091182';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735113';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091195';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891299';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891301';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001277';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891304';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001299';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735126';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100891316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100735133';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091207';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101091220';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360821';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360871';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360876';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360882';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101360891';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001309';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001339';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA101001340';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:40' WHERE product_id = 'CMA100089574';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891320';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001343';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089575';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387313';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387315';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387320';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001362';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091237';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387337';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082338';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735166';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001371';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360896';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360911';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735169';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360938';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360955';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735188';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360963';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360978';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891332';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891349';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101360987';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361006';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091283';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361012';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091314';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361022';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091907';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735206';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001389';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891360';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091916';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891379';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091920';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091923';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091322';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091928';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091334';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091344';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001399';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091931';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091350';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091356';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891396';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091370';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891403';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891405';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891407';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361060';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361094';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891409';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891412';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001404';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891414';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091373';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091383';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891464';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891475';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891486';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387374';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091935';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387380';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387396';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089576';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891490';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735208';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100891502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735256';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735257';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735258';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735260';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091395';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387459';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735269';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387484';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735284';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001483';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735292';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361107';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735322';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091950';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001523';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001531';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001538';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735374';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735399';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735403';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735481';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735489';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735523';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001546';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387563';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091447';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091464';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001584';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361139';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361242';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361247';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092235';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361257';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361314';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091505';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091529';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091532';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361326';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361342';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089577';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735542';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092247';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082342';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735591';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735614';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361352';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735618';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361401';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735620';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091568';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735621';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091573';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735623';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735625';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387624';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361411';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735627';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361441';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361449';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091576';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001689';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001701';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091585';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387640';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091599';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091604';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735628';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091609';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091626';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387691';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001707';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001711';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001721';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001725';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361475';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001729';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361480';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361486';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091643';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387743';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361492';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361503';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092318';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091667';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091674';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735638';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089579';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387759';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361528';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387870';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092729';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089580';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361538';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001737';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082343';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735647';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092798';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091684';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092823';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092830';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361561';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735655';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092839';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001752';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092842';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001770';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735663';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092852';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001778';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001790';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082344';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001795';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101092859';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091776';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001817';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091783';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735695';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735704';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735710';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361572';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361603';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001846';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361610';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361619';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361626';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091790';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735766';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091797';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735773';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361637';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091807';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001918';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001923';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361656';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361690';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735782';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091829';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091837';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735790';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735798';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091843';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091844';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001956';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361706';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361717';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735844';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735845';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361761';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735856';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735874';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101001963';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101387913';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100089582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100082345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735916';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735924';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735929';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735934';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA100735942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091888';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091891';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091899';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361801';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091913';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101361826';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:41' WHERE product_id = 'CMA101091918';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100735944';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361835';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091938';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091946';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100735960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100735995';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100735996';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091970';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091974';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101387927';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091975';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091980';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091985';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361846';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361915';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361923';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091989';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091992';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101091995';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101387938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082347';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002061';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002132';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002135';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736040';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089585';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092008';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092033';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101387950';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082348';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092039';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092091';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092098';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002152';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002168';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736060';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736071';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736093';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361984';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092111';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092130';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736099';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092132';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101361995';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092140';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362019';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101387971';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362034';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736100';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002194';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362061';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362069';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736114';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092142';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092145';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101387980';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362089';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736138';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092150';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092153';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092159';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092161';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092165';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002204';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736157';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362101';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082349';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092169';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101387989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736188';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736200';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092186';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736254';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092226';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388052';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089587';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002241';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388079';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388091';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082352';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002256';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002264';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002265';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736360';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092240';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092252';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092256';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092266';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736376';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736388';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736423';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388305';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002317';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089589';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092281';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736424';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736441';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736456';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736508';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736511';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092286';
COMMIT;
