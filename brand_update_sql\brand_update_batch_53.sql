BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101535992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101536262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101536993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101537996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538275';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101538994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:03' WHERE product_id = 'CMA101539189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101539988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101540998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101541979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542434';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542913';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101542989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:04' WHERE product_id = 'CMA101543437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101543996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101544995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101545978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101546956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:05' WHERE product_id = 'CMA101547732';
COMMIT;
