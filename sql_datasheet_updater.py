#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Datasheet URL Updater
用于读取SQL文件并生成UPDATE语句来更新datasheet_url字段
适用于Cloudflare D1数据库
"""

import os
import re
import glob
import time
from datetime import datetime
from typing import List, Tuple, Dict
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading


def extract_insert_statements(sql_content: str) -> List[str]:
    """
    从SQL内容中提取所有INSERT OR REPLACE语句
    使用分号分割，然后过滤出INSERT语句
    """
    # 先按分号分割所有语句
    statements = []

    # 处理可能跨行的语句，先移除注释
    lines = sql_content.split('\n')
    clean_lines = []
    for line in lines:
        # 移除行注释
        if '--' in line:
            line = line[:line.index('--')]
        clean_lines.append(line)

    clean_content = '\n'.join(clean_lines)

    # 按分号分割语句
    raw_statements = clean_content.split(';')

    for stmt in raw_statements:
        stmt = stmt.strip()
        if not stmt:
            continue

        # 检查是否是INSERT语句（包括INSERT OR REPLACE和INSERT INTO）
        stmt_upper = stmt.upper()
        if ('INSERT OR REPLACE' in stmt_upper or 'INSERT INTO' in stmt_upper) and 'VALUES' in stmt_upper:
            statements.append(stmt + ';')  # 重新添加分号

    return statements


def parse_insert_statement(insert_stmt: str) -> Tuple[str, str]:
    """
    解析INSERT语句（包括INSERT OR REPLACE和INSERT INTO），提取product_id和datasheet_url
    返回: (product_id, datasheet_url)
    """
    try:
        # 清理INSERT语句，移除多余的空白字符
        insert_stmt = ' '.join(insert_stmt.split())

        # 检查是否是支持的INSERT语句类型
        stmt_upper = insert_stmt.upper()
        if not (('INSERT OR REPLACE' in stmt_upper or 'INSERT INTO' in stmt_upper) and 'VALUES' in stmt_upper):
            return None, None

        # 提取VALUES部分的内容，使用更精确的正则表达式
        values_match = re.search(r"VALUES\s*\((.+)\)\s*;?\s*$", insert_stmt, re.DOTALL | re.IGNORECASE)
        if not values_match:
            return None, None

        values_content = values_match.group(1).strip()

        # 改进的字段值分割算法
        values = []
        current_value = ""
        in_quotes = False
        quote_char = None
        paren_count = 0
        bracket_count = 0

        i = 0
        while i < len(values_content):
            char = values_content[i]

            # 处理引号
            if char in ["'", '"'] and (i == 0 or values_content[i-1] != '\\'):
                if not in_quotes:
                    in_quotes = True
                    quote_char = char
                elif char == quote_char:
                    # 检查是否是转义的引号
                    if i + 1 < len(values_content) and values_content[i + 1] == quote_char:
                        current_value += char + char  # 添加转义的引号
                        i += 1  # 跳过下一个引号
                    else:
                        in_quotes = False
                        quote_char = None
            # 处理括号和大括号
            elif char == '(' and not in_quotes:
                paren_count += 1
            elif char == ')' and not in_quotes:
                paren_count -= 1
            elif char == '{' and not in_quotes:
                bracket_count += 1
            elif char == '}' and not in_quotes:
                bracket_count -= 1
            # 处理逗号分隔符
            elif char == ',' and not in_quotes and paren_count == 0 and bracket_count == 0:
                values.append(current_value.strip())
                current_value = ""
                i += 1
                continue

            current_value += char
            i += 1

        # 添加最后一个值
        if current_value.strip():
            values.append(current_value.strip())

        # 清理值（去除外层引号）
        cleaned_values = []
        for value in values:
            value = value.strip()
            if len(value) >= 2:
                if (value.startswith("'") and value.endswith("'")) or \
                   (value.startswith('"') and value.endswith('"')):
                    value = value[1:-1]
            cleaned_values.append(value)

        # 根据字段顺序提取product_id和datasheet_url
        # 字段顺序: product_id, model, brand_id, price_key, stock_key, datasheet_url, ...
        if len(cleaned_values) >= 6:
            product_id = cleaned_values[0]
            datasheet_url = cleaned_values[5]

            # 验证提取的数据
            if product_id and datasheet_url and datasheet_url != 'NULL':
                return product_id, datasheet_url

        return None, None

    except Exception as e:
        # 如果解析失败，返回None
        print(f"解析INSERT语句时出错: {e}")
        return None, None


def generate_update_statements(product_data: List[Tuple[str, str]]) -> str:
    """
    生成UPDATE语句，确保只输出有效的UPDATE语句
    """
    if not product_data:
        return ""

    update_statements = []
    update_statements.append("-- Cloudflare D1 数据库 datasheet_url 更新语句")
    update_statements.append(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    update_statements.append(f"-- 总计 {len(product_data)} 条UPDATE语句")
    update_statements.append("")

    valid_count = 0
    for product_id, datasheet_url in product_data:
        # 验证数据有效性
        if not product_id or not datasheet_url:
            continue

        # 清理和验证product_id
        product_id = str(product_id).strip()
        if not product_id or product_id.upper() == 'NULL':
            continue

        # 清理和验证datasheet_url
        datasheet_url = str(datasheet_url).strip()
        if not datasheet_url or datasheet_url.upper() == 'NULL':
            continue

        # 验证URL格式（基本检查）
        if not (datasheet_url.startswith('http://') or datasheet_url.startswith('https://')):
            continue

        try:
            # 转义单引号和其他特殊字符
            escaped_url = datasheet_url.replace("'", "''")
            escaped_id = product_id.replace("'", "''")

            # 生成UPDATE语句
            update_stmt = f"UPDATE products202503 SET datasheet_url = '{escaped_url}' WHERE product_id = '{escaped_id}';"

            # 验证生成的语句不包含INSERT关键字
            if 'INSERT' not in update_stmt.upper():
                update_statements.append(update_stmt)
                valid_count += 1
            else:
                print(f"警告: 跳过包含INSERT的异常语句: {product_id}")

        except Exception as e:
            print(f"生成UPDATE语句时出错 (product_id: {product_id}): {e}")
            continue

    # 更新注释中的实际数量
    if len(update_statements) >= 3:
        update_statements[2] = f"-- 实际生成 {valid_count} 条有效UPDATE语句"

    return "\n".join(update_statements)


def validate_sql_output(sql_content: str) -> str:
    """
    验证SQL输出，确保只包含UPDATE语句和注释
    """
    lines = sql_content.split('\n')
    validated_lines = []

    for line in lines:
        line = line.strip()
        if not line:
            validated_lines.append(line)
            continue

        # 允许注释行
        if line.startswith('--'):
            validated_lines.append(line)
            continue

        # 只允许UPDATE语句
        if line.upper().startswith('UPDATE') and 'SET datasheet_url' in line.upper():
            validated_lines.append(line)
            continue

        # 跳过任何其他类型的语句
        print(f"警告: 跳过非UPDATE语句: {line[:100]}...")

    return '\n'.join(validated_lines)


# 删除了process_insert_batch函数，现在直接在文件级别并行处理


def process_single_file(sql_file: str) -> List[Tuple[str, str]]:
    """
    处理单个SQL文件，直接处理INSERT语句（不再使用内部多线程）
    """
    try:
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(sql_file, 'r', encoding='gbk') as f:
                sql_content = f.read()
        except UnicodeDecodeError:
            print(f"警告: 无法读取文件 {sql_file}，跳过")
            return []

    # 提取INSERT语句
    insert_statements = extract_insert_statements(sql_content)
    print(f"[线程] 处理文件: {os.path.basename(sql_file)} - 找到 {len(insert_statements)} 条INSERT语句")

    if not insert_statements:
        return []

    # 直接处理所有INSERT语句，使用列表推导式提高性能
    file_product_data = []
    valid_count = 0

    for i, insert_stmt in enumerate(insert_statements):
        if i % 1000 == 0 and i > 0:  # 每1000条显示一次进度
            print(f"[线程] {os.path.basename(sql_file)}: 已处理 {i}/{len(insert_statements)} 条语句")

        product_id, datasheet_url = parse_insert_statement(insert_stmt)
        if product_id and datasheet_url:
            file_product_data.append((product_id, datasheet_url))
            valid_count += 1

    print(f"[线程] 完成文件: {os.path.basename(sql_file)} - 提取到 {valid_count} 条有效数据 (总语句: {len(insert_statements)})")
    return file_product_data


def process_sql_files(input_folder: str, output_file: str = None) -> None:
    """
    使用多线程处理指定文件夹中的所有SQL文件
    """
    # 确保输入文件夹存在
    if not os.path.exists(input_folder):
        print(f"错误: 文件夹 '{input_folder}' 不存在")
        return

    # 查找所有SQL文件
    sql_pattern = os.path.join(input_folder, "*.sql")
    sql_files = glob.glob(sql_pattern)

    if not sql_files:
        print(f"在文件夹 '{input_folder}' 中未找到SQL文件")
        return

    print(f"找到 {len(sql_files)} 个SQL文件")
    print("开始真正的多线程并行处理...")

    all_product_data = []
    completed_files = 0
    start_time = time.time()

    # 使用更多线程并行处理文件
    max_workers = min(12, len(sql_files))  # 最多12个线程并行处理文件
    print(f"使用 {max_workers} 个线程并行处理文件")
    print(f"开始时间: {datetime.now().strftime('%H:%M:%S')}")

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有文件处理任务
        future_to_file = {executor.submit(process_single_file, sql_file): sql_file
                         for sql_file in sql_files}

        # 收集结果，显示进度
        for future in as_completed(future_to_file):
            sql_file = future_to_file[future]
            try:
                file_data = future.result()
                all_product_data.extend(file_data)
                completed_files += 1
                elapsed_time = time.time() - start_time
                avg_time_per_file = elapsed_time / completed_files
                remaining_files = len(sql_files) - completed_files
                estimated_remaining = avg_time_per_file * remaining_files

                print(f"进度: {completed_files}/{len(sql_files)} 文件已完成 ({completed_files/len(sql_files)*100:.1f}%)")
                print(f"  当前总数据: {len(all_product_data)} 条")
                print(f"  已用时间: {elapsed_time:.1f}秒，预计剩余: {estimated_remaining:.1f}秒")
                print(f"  平均每文件: {avg_time_per_file:.2f}秒")
                print("-" * 50)
            except Exception as exc:
                completed_files += 1
                print(f'文件 {os.path.basename(sql_file)} 处理出错: {exc}')

    total_time = time.time() - start_time
    print(f"\n多线程处理完成!")
    print(f"总用时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    print(f"总共提取到 {len(all_product_data)} 条产品数据")
    print(f"平均处理速度: {len(all_product_data)/total_time:.1f} 条/秒")

    if not all_product_data:
        print("没有找到有效的产品数据")
        return

    # 生成UPDATE语句
    print("生成UPDATE语句...")
    update_sql = generate_update_statements(all_product_data)

    # 验证SQL输出，确保只包含UPDATE语句
    print("验证SQL输出...")
    validated_sql = validate_sql_output(update_sql)

    # 确定输出文件名
    if output_file is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"update_datasheet_urls_{timestamp}.sql"

    # 确保输出目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir, exist_ok=True)
            print(f"创建输出目录: {output_dir}")
        except Exception as e:
            print(f"警告: 无法创建输出目录 {output_dir}: {e}")
            # 如果无法创建目录，则在当前目录下创建文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"update_datasheet_urls_{timestamp}.sql"
            print(f"将在当前目录创建文件: {output_file}")

    # 写入输出文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(validated_sql)

        # 统计实际的UPDATE语句数量
        update_count = validated_sql.count('UPDATE products202503 SET datasheet_url')

        print(f"\nUPDATE语句已生成到文件: {output_file}")
        print(f"包含 {update_count} 条有效UPDATE语句")
        print(f"原始数据: {len(all_product_data)} 条，有效输出: {update_count} 条")

    except Exception as e:
        print(f"错误: 无法写入输出文件 {output_file}: {e}")


def main():
    """
    主函数
    """
    # 固定输入文件夹路径
    input_folder = r"M:\完整sql\NEWproducts202503"

    # 检查输入文件夹是否存在，如果不存在则尝试其他可能的路径
    if not os.path.exists(input_folder):
        print(f"警告: 指定的输入文件夹不存在: {input_folder}")

        # 尝试在当前目录下查找可能的SQL文件夹
        possible_folders = [
            "NEWproducts202503",
            "products202503",
            "sql_files",
            "."  # 当前目录
        ]

        for folder in possible_folders:
            if os.path.exists(folder):
                sql_files = glob.glob(os.path.join(folder, "*.sql"))
                if sql_files:
                    input_folder = folder
                    print(f"找到SQL文件夹: {input_folder} (包含 {len(sql_files)} 个SQL文件)")
                    break
        else:
            print("错误: 未找到包含SQL文件的文件夹")
            print("请确保以下任一文件夹存在并包含SQL文件:")
            for folder in possible_folders[:-1]:  # 排除当前目录
                print(f"  - {folder}")
            return

    # 输出文件名 - 在当前目录创建
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"update_datasheet_urls_{timestamp}.sql"

    print("SQL Datasheet URL Updater")
    print("=" * 50)
    print(f"输入文件夹: {input_folder}")
    print(f"输出文件: {output_file}")
    print()

    # 处理SQL文件
    process_sql_files(input_folder, output_file)

    print("\n处理完成!")


if __name__ == "__main__":
    main()
