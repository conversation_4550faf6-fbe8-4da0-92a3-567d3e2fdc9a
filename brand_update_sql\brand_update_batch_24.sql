BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748534';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748554';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101060';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016912';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016954';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016957';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090421';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016973';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016989';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090422';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748587';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101016994';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017004';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017024';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101072';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083032';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748589';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748605';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090424';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017186';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017195';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748618';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748631';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748678';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090425';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748692';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090426';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017401';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101105';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083034';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017410';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017429';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101124';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748724';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090428';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017491';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017511';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748748';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017519';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017527';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083035';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101130';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748797';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017576';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748822';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017608';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748849';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100083037';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101101206';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100090431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA100748886';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:58' WHERE product_id = 'CMA101017656';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748889';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748923';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748924';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748930';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748945';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083038';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017723';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748959';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101207';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100748999';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017749';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749005';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017812';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017820';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017840';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101228';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749047';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749074';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017900';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749115';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749124';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017944';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749142';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749145';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017983';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083040';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749167';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090435';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101242';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090437';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017991';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090438';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101017998';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018012';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083041';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018032';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749247';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749268';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018054';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083042';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083043';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101252';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749290';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749300';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749311';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101297';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018179';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018185';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101335';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749354';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018197';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749382';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018225';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749415';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749454';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101340';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018342';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018355';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018356';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101352';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101367';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101387';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090443';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101392';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749498';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101018388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100083047';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100090444';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101414';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101455';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749503';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA101101467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:59' WHERE product_id = 'CMA100749537';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083048';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018408';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749544';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101487';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749547';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018442';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090445';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090446';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090447';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749567';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101492';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018486';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101528';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749588';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018509';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101537';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101545';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101559';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083169';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101601';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090450';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018558';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018608';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083170';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101615';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018634';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101626';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018648';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018681';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018691';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101640';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101646';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090453';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018723';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018777';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101655';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018805';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018810';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018813';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101018821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100749815';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090623';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101663';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101668';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA101101674';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090624';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100090625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:00' WHERE product_id = 'CMA100083174';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101682';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101691';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100749971';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101018823';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100749991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083175';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101764';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101018886';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090627';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101018898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101018922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083176';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101796';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101018974';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019069';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019073';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100749995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100749998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101808';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101818';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101823';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101833';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101846';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101847';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101851';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019110';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090628';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101884';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019127';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101888';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019141';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090629';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019167';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750003';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090630';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101914';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019172';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019230';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019250';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750018';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750036';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090631';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019262';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750041';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019289';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019301';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019312';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090632';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090633';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090634';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090635';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019319';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019330';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019337';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019439';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083179';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019451';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019574';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101101960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083180';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019585';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019604';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102039';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102044';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102068';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102069';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102078';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102088';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102106';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102117';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102132';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090636';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102139';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102150';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019631';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102155';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102158';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750059';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750083';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102176';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102184';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019664';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102193';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102201';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102213';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019700';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102223';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102236';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102239';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083181';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090639';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019705';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019720';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019726';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090640';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019728';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102251';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102261';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019764';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102265';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750150';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019873';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083182';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083183';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019931';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102276';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750229';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101019987';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102331';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101020052';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102338';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750253';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102354';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100090642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100083184';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102361';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102364';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA101102367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:01' WHERE product_id = 'CMA100750305';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102372';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102376';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750339';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102401';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102405';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020079';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102408';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750400';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020104';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020134';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102417';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102419';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102421';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020148';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020160';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102424';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090643';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020175';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750430';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750481';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750485';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020222';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020234';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020252';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102462';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750507';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102478';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102481';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102485';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102487';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020278';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750518';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102491';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102497';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102499';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102503';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020319';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102504';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020335';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102507';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750524';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102509';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020350';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020363';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102525';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020364';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020379';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083189';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020413';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750550';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750553';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750585';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090645';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102580';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083191';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750615';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750628';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750637';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750652';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020445';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020463';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102624';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102655';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102671';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083193';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020476';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020549';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020553';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020561';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020565';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090647';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102695';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102712';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083194';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102720';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020611';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102727';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083195';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102739';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102746';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020625';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083196';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750704';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020660';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020675';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102754';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102761';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090648';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020682';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750723';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020695';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020700';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020708';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102763';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750725';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020713';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020746';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020847';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020891';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020894';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090649';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750773';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020915';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102793';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102798';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020918';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750776';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102805';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020936';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750787';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102834';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102848';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102852';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102855';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101020982';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750809';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102871';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102874';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102877';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750810';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102886';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021054';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750820';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750832';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102897';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102902';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083303';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021336';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083305';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102919';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750860';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750863';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750890';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021476';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750892';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021515';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021523';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021528';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021553';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100090650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100083307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750927';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750986';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750988';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750989';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101021696';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750995';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100750998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751022';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101102997';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA101103004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:02' WHERE product_id = 'CMA100751025';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103016';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103020';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103026';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100090651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083308';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751044';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103034';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101021713';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751046';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103071';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103085';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103091';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101021761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751112';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103092';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100090652';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751119';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101021813';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101021890';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101021898';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103107';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103131';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103133';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751131';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103137';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103166';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103170';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751154';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751158';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103192';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751160';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751169';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103232';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103244';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751186';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751212';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751218';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119156';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103264';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022249';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103272';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022269';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022298';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103284';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022322';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022342';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103333';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022359';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022465';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103353';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083313';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103358';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103392';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103395';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103401';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022508';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103410';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022581';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751226';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022690';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022703';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103424';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119158';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022733';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083317';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022773';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022793';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103439';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103451';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103454';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022799';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103456';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103463';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022818';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022829';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022850';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083318';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119159';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103468';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103482';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103485';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083319';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103491';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103551';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103553';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103558';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103561';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022859';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751278';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103573';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103580';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022957';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101022976';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103608';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103615';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751283';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083320';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101023017';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101023047';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101023055';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101023056';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103633';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100119161';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101023061';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100751321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA100083321';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:03' WHERE product_id = 'CMA101103661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751341';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751372';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751389';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751390';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103697';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023099';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100083322';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100083323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751525';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023157';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023161';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103777';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103802';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100083324';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100083326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751579';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751582';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023169';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103823';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023258';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023262';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023264';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103832';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103839';
COMMIT;
