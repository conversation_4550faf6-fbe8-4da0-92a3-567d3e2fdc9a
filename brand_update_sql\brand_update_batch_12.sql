BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070639';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100713352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100713358';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070649';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713362';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332055';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045393';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033069';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100088314';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079807';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070659';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880576';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100088315';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880579';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070676';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880583';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070700';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070708';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070738';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100494572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079809';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070741';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111001';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880587';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880600';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880603';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070891';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070897';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880605';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070904';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880612';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880616';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880619';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880629';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070922';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033070';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070927';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880636';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713371';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100494573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079810';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070967';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033071';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111002';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880641';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070985';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880657';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100088316';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880661';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880663';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332056';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880664';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880668';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070993';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045394';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880671';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045397';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880692';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880694';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880695';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713397';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332057';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332059';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100494574';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045501';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033072';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111003';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100088317';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079811';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111004';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332062';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332063';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880704';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880710';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101070999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071052';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332192';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071056';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045503';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071063';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045504';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071069';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880719';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071072';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880727';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880733';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071119';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880757';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880763';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880772';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071184';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880778';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880780';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880791';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880792';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880798';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880808';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880813';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713407';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880827';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071201';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071235';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880848';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880868';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071241';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111005';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880878';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079812';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071261';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071263';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071266';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071277';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332195';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071281';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880887';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071285';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713417';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033074';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100494575';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100088318';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045506';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332196';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880891';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880905';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033076';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079813';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111006';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071288';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100494576';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332197';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100494577';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071299';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880913';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880922';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071322';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071327';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100494578';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071340';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071345';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111007';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071347';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045508';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071351';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100088319';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100713547';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100033077';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100088320';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111009';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332206';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100332208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100079816';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100111010';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045509';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880928';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071359';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071388';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071392';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071395';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071401';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100880935';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071412';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045511';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071414';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA100045512';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:05' WHERE product_id = 'CMA101071427';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880949';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880955';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071428';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071448';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071450';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071479';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332209';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071481';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332210';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880982';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071489';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880985';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071504';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713619';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045513';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713638';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088321';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033078';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088322';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880991';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111011';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111012';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332211';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332212';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071511';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079817';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494579';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332213';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332214';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071532';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071540';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880992';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071561';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071564';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071566';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071569';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071571';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100880998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071577';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713653';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494580';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079818';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111015';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881016';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111016';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071578';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033079';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881022';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033080';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071591';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071644';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045516';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713673';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071661';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071673';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332215';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071680';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071688';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071693';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071695';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881030';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332216';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033081';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071703';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111017';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111018';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881046';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111019';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713745';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033083';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045518';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088323';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881072';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111020';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045520';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033084';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079820';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079821';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111022';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332217';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088324';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088325';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111023';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494582';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071708';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332219';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071713';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111025';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332220';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071719';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071731';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071736';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071742';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088326';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045521';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332223';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071745';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071753';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079822';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881091';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881108';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045522';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033086';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881113';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881122';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111027';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071758';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071777';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071781';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881141';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071785';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881147';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332224';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494583';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071792';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071799';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071810';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071814';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881154';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881166';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088327';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332225';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071821';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071832';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071885';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881180';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071886';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071888';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071891';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071895';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881189';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881216';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088328';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332226';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881219';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881226';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045523';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033087';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071898';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079932';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071918';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071926';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071930';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713791';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111029';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071962';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033088';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071970';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881231';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071972';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111030';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071981';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071984';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045524';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071989';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332228';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071990';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332230';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071995';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100332231';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101071998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072001';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100033089';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079933';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045525';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111032';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045526';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494585';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881237';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881261';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881270';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494587';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881272';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881274';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100079935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713809';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045527';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881282';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100494588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072003';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072055';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881300';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072058';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713820';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072072';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072089';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072104';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100088329';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072108';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072116';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045528';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881308';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100111033';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072122';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881315';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713840';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100045529';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100881325';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA101072175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:06' WHERE product_id = 'CMA100713850';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111034';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100088330';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100033090';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881334';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045530';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079936';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100332233';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881350';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072177';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111035';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881352';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494755';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881367';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072187';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100332234';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100332235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100713860';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494756';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072266';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072278';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100713866';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881369';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881377';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100713881';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100713884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100713890';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881387';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079937';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100332237';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045531';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881390';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881394';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045532';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881396';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045533';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881399';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881401';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100033091';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100332240';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100033092';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072331';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100713892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714020';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100088331';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111042';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111043';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333966';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494758';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881406';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494759';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881408';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881416';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494760';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079938';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072383';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045534';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045536';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100033093';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881418';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881434';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072406';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111044';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111045';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100033094';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072417';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333967';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881441';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881451';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714149';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072445';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072450';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072454';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072458';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079939';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045537';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072460';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100088332';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881471';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881483';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494761';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881487';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100033095';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333969';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100088333';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881491';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045538';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079940';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881494';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881502';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881506';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111048';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881509';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881519';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881532';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881536';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881543';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881545';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881546';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111049';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881547';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881557';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881560';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714151';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714165';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111050';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079941';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333970';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881577';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333971';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045541';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072486';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045542';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881595';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333972';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079943';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045544';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072542';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881598';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100088334';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100926623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079944';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045545';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111210';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333975';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881607';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494763';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100926649';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333976';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881609';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714206';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072555';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111212';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045548';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045549';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333977';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100088335';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100045551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079945';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100088336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072588';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881612';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881614';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881616';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881618';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714237';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072609';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072615';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100927383';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA101072633';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100494764';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333978';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881624';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100333980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100079947';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100111213';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714313';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100714321';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881634';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881637';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:07' WHERE product_id = 'CMA100881640';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881643';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881645';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881648';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100333981';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100927610';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072656';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714330';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072668';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088337';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111215';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494765';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072676';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072681';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714354';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714359';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714377';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088338';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881677';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045553';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072690';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045555';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072705';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045556';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072707';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881679';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072719';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045749';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714389';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714401';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881688';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881694';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881697';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100333985';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072732';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045750';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111216';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088339';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100333986';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100333987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714408';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079949';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100927768';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079951';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881701';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881742';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881753';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881758';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088514';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334147';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111217';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881773';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111218';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881789';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111219';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881795';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881819';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881827';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111220';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111221';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088515';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881829';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072741';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334148';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100928616';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072758';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881831';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881833';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881837';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072773';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881839';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072791';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881841';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072792';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881844';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714439';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072797';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881846';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881858';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714449';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881863';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494768';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334149';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714485';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072799';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881877';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100928954';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088516';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100929284';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111397';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045752';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881880';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100929349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714488';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334150';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881883';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881885';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494769';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881888';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881892';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881896';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714541';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714572';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714585';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334151';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079952';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334153';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100929542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079953';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088517';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100929615';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045753';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334303';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079954';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881904';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881912';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334305';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881916';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714607';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072841';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334306';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881920';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881925';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111399';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100929705';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494771';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100929718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079955';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072849';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334308';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334309';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111400';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334310';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045754';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881927';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072854';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079957';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881944';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072872';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072876';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494772';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881946';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072879';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334312';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100929835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079958';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714673';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714704';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088520';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045755';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494773';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334314';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072883';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072904';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881949';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881957';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA101072911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100079959';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100930253';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045757';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881960';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100494774';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100088521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714734';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334315';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881968';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334316';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881971';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334317';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100045758';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100111402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100714741';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100881972';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:08' WHERE product_id = 'CMA100334318';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100881978';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100088522';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100881980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100881984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714800';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714803';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100079961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100080977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714857';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100045759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714860';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100088523';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100881986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714866';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714874';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494776';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100088524';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101072948';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100881991';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882009';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882010';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100111403';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101072964';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101072989';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101072993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714895';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100045760';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100080978';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882023';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101072999';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100088525';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101073018';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714917';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494777';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714962';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100111405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100080979';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882024';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882033';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494778';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882036';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100714980';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101073024';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101073034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100080981';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715029';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100111407';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100045761';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882049';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334325';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100088526';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494779';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882051';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100111408';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882059';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100088527';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101073040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100080982';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100045762';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334326';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494780';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715036';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715119';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101073078';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100080983';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882098';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100045764';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100111409';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494832';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715136';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334329';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100088528';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100045765';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882105';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882116';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882119';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100882121';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101073118';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101073120';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100334331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100080984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100715155';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA101363312';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100045766';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:09' WHERE product_id = 'CMA100494834';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363319';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363331';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100088529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715191';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100334332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715195';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100494835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715200';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080985';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882129';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073122';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363342';
COMMIT;
