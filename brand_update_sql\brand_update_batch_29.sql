BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045723';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128374';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045742';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045763';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045784';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045792';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045811';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045820';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772606';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112042';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128378';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772628';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045824';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045831';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045836';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045841';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045859';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112045';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772647';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045899';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112047';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772686';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772730';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772738';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128383';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045918';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128385';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045944';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112048';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045963';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045975';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112049';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101045983';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772751';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128386';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046019';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046022';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046023';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112050';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772837';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046036';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772840';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128388';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772871';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772906';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046107';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046119';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772912';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112054';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772923';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046143';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046154';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046174';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046193';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046247';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046262';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046265';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128532';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046274';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112188';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128534';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772958';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046279';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046303';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772970';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100772974';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773033';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773041';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046317';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773051';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046361';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046373';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773079';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046386';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128541';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046409';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046422';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773088';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128543';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046426';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112190';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773127';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773133';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773213';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046454';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773247';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773253';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046496';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128548';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100112193';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128551';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA101046565';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100128552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:29' WHERE product_id = 'CMA100773310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773386';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100128553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112194';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773475';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100128554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046595';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100128555';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046672';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046678';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100128556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112196';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046691';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046697';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046724';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046738';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046746';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046755';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112197';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100128557';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773582';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100128558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773603';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773623';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773647';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100746863';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100746877';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112198';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046960';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773814';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046979';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101046984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773823';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773833';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747268';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773946';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047001';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047010';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047022';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773968';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047031';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747490';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047065';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047079';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047085';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047102';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747520';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047118';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100773985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774020';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047164';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774035';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112203';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774046';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747852';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112204';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047323';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047340';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047348';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774066';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774117';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774122';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047425';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047439';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747955';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112206';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774133';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747979';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100747987';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047463';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112207';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047493';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047496';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774218';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047513';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047519';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047537';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047542';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047547';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047549';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047560';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774224';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047568';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774236';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047610';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047642';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047666';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047673';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100748196';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100748203';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100748213';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100748417';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774262';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112208';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774268';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774273';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774339';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100748876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112209';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774447';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100112210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100774452';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100749325';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA101047870';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:30' WHERE product_id = 'CMA100749395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774558';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100749702';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774568';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774635';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774647';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047929';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774653';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101047966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774691';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048025';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774704';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100749760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774713';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100750496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112215';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048058';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048077';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048092';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100750506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112216';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751080';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751083';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048106';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751114';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048125';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751166';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048181';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774805';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751184';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774813';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048239';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774823';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774838';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048329';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774843';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774852';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774858';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774866';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048388';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048395';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774879';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048412';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751328';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048466';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112228';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048473';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048496';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048500';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751330';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751332';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048503';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048565';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751338';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751346';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048619';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048643';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751360';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774955';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048732';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100774995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112229';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775032';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751373';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751375';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751380';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048786';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751385';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775061';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775069';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048845';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048906';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751406';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048912';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751527';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775080';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112231';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775120';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775153';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775160';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048970';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048972';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775163';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101048975';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101049009';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775305';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100751835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA101049011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100112233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775345';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100752140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:31' WHERE product_id = 'CMA100775350';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049021';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775401';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049049';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049065';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752385';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049078';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049087';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049121';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752422';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049168';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775446';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112237';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049220';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775507';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775509';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775532';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049273';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049280';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775550';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775562';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752680';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049293';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775566';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775629';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049333';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049337';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049340';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775670';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752801';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049348';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775676';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775751';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752856';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775757';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752876';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752883';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775783';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112239';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752914';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775808';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752931';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049408';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049416';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100752972';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049419';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049436';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049444';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049446';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112240';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049489';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775839';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775852';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753115';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775898';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753133';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753141';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100775986';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049527';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049549';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776001';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776018';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112245';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049626';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753841';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049644';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049660';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100753889';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776111';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049727';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049729';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100754198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776178';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049767';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776366';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049819';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100754331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776375';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776380';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776382';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049918';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049930';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776457';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100754538';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100754549';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101049952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112250';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100754622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776503';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101050015';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101050037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776593';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101050122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112251';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101050171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100112252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100776627';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA100755031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101050238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:32' WHERE product_id = 'CMA101050241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050273';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050275';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776666';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050280';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050290';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050292';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050301';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050313';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100112253';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050368';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776776';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100112254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776791';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776814';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776871';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050496';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100112255';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100112256';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050570';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050579';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050583';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776986';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101050589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100112257';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755793';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755804';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100776998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365187';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777152';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100755867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777198';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100756161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777283';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100757970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777302';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100757972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365261';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100758335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA101365265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:33' WHERE product_id = 'CMA100777322';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365403';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777423';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777441';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777501';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777503';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365818';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758947';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101365821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777573';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100758975';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759002';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759016';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759020';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759026';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366046';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759073';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759075';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759077';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759085';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759091';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366049';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366056';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777630';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759130';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777642';
COMMIT;
