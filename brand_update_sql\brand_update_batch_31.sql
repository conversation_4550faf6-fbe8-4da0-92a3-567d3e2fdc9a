BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781527';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781595';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370424';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370473';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766122';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781826';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781852';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766250';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370615';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781905';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781928';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370663';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100781996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782012';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766294';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370791';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766415';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766427';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766451';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370802';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370805';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370813';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370876';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370912';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370938';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370953';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766601';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370969';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370980';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766623';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766625';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101370986';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766673';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766677';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766679';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766683';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371017';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766698';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766702';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371092';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782403';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782458';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766778';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782520';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782528';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782556';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100766946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371127';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371136';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782646';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371179';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767111';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767115';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371183';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782689';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371196';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767234';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371332';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371352';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782765';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767472';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767476';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767482';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767487';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767490';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100782828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA101371420';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:38' WHERE product_id = 'CMA100767671';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100767680';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100767687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371456';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100767708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782879';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100767715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371593';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100767893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371635';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100767964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782985';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100767990';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100768010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100782994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371706';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100768079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783034';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100768675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101371999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372106';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100783985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA100784222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:39' WHERE product_id = 'CMA101372882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101372907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101372952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101372959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101373975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100784939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA100785727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:40' WHERE product_id = 'CMA101374500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100785972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101374999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786850';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786913';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100786988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375730';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101375997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787520';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA101376427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:41' WHERE product_id = 'CMA100787983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA101376484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100787986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100787991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:42' WHERE product_id = 'CMA100787994';
COMMIT;
