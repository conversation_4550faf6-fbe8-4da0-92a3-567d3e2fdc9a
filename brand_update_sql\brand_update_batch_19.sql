BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357342';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381568';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889350';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087813';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889362';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087831';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732270';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998328';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998330';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381653';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357353';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357376';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497456';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087874';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497457';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357396';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998351';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357403';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357421';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089540';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889387';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889390';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732275';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357423';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357433';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381752';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357450';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998406';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357459';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381818';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357483';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998468';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998499';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357489';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357505';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082220';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087894';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082221';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089541';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497459';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381871';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381963';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087905';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497460';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087915';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087927';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732312';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381970';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497461';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087952';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382001';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087960';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497462';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087978';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497463';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357520';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087988';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357534';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087989';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998513';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998522';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087993';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889395';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088021';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889398';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889402';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889404';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088063';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889407';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088081';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497464';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889424';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497465';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889428';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889431';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998531';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998536';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497466';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357553';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088087';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357560';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088092';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088105';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998543';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998559';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732314';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497467';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732349';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889456';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889491';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088110';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088120';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089543';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382352';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082223';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889505';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889542';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732386';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089544';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889544';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497468';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357593';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497469';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732474';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998589';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998594';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732511';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889558';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382501';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998606';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382567';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889564';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382579';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088142';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889569';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088174';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998618';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088181';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089545';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732518';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382593';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082224';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497471';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889580';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497472';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088183';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088192';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088206';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088218';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889585';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088221';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889588';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357616';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357655';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497473';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889590';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088225';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088319';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889632';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889664';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889665';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357667';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889667';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382903';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088357';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088376';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357687';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088382';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382908';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357705';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082225';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088387';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998687';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088391';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088394';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101382916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100998712';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089546';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889674';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088396';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088418';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101088426';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889678';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889683';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889687';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357710';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889688';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357717';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889689';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357727';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100089547';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357731';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357838';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357855';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357861';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357863';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357866';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998719';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889690';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998770';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889705';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101382998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088439';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100089548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082226';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088462';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088474';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088476';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998778';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998782';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082227';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383009';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497474';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889709';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357877';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497475';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357887';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082228';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088480';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082229';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357889';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357894';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082231';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889711';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998866';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998872';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732562';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998879';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998884';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889718';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357899';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357906';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889720';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889724';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998897';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889727';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497476';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998924';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088490';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088502';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088504';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998935';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088509';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088513';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732569';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088522';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497477';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082232';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088532';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088541';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497478';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383061';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088542';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088551';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088552';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088561';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357912';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357925';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088590';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357939';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088608';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732604';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732638';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100089549';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357954';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357973';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101357985';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358006';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088651';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358013';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383073';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358026';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358048';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358076';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732661';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732667';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358094';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358099';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088699';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358114';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732688';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732704';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889728';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998938';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998941';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889729';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998945';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732712';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088723';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732724';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732731';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358151';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383150';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358157';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998949';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889732';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497479';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088817';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358167';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383182';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732733';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088840';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100089550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732760';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383250';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358181';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358218';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383295';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732786';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732794';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732807';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732811';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358248';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383387';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358284';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732818';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358296';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358309';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383440';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732834';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383526';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889736';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100998969';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383544';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732874';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358338';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358370';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358377';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100089551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082234';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383562';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383575';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889739';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358383';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358413';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889742';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088843';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889746';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358419';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088863';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889750';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358428';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088869';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088877';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088885';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088901';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889752';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358443';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088906';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088910';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889797';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088913';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088921';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088927';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088939';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358488';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088958';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889801';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088970';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088985';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088989';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088992';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383590';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358506';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383634';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358520';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358524';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383636';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889804';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101088996';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089003';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358529';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089014';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358551';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889810';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100082235';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089041';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732885';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089088';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358556';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358578';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889826';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089095';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358589';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089108';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358603';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358610';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889840';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889846';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889855';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101089114';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358614';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889859';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358623';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889863';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497481';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889866';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100889872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732911';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100999010';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497482';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100497483';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100089552';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358631';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101383640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732921';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA101358640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:36' WHERE product_id = 'CMA100732933';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889879';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889886';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100732940';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101383699';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089156';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100082236';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089221';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358663';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100732945';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358676';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089322';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999077';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358683';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089340';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089349';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100732954';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497484';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733019';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358712';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889907';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358723';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358734';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101383943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999085';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999123';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999126';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999132';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889915';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889918';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889920';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889924';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358741';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889925';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497485';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358749';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889926';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999135';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999182';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100089553';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999185';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100082237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089381';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089402';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889928';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497486';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100089554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999293';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999294';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089420';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889935';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889945';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889946';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889947';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384265';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358764';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358883';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100082238';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089453';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358898';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733073';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100089555';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089504';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089515';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999296';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497676';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384604';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384616';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089527';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358950';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089598';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999332';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999348';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889953';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889958';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889963';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889974';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889977';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889983';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100082240';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089615';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089624';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100089556';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497677';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999364';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889993';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100889998';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890001';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890007';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890010';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890012';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358953';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890026';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358968';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384679';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999368';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497678';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999389';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101358983';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100082241';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089627';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890034';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890056';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890061';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890070';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733099';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101359006';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100089557';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101359030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100082242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733149';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089653';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890080';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890106';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384728';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890113';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497680';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497681';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497682';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101359040';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100089558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100082243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733165';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384734';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890124';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890144';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089725';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101359070';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100497683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733176';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384743';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384746';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384755';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101359096';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890147';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101089734';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100890167';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999404';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100999433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733185';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA101384767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:37' WHERE product_id = 'CMA100733195';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359102';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100082245';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890175';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890181';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999442';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497684';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733212';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359112';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089765';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733233';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999453';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359139';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999455';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999461';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100082246';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890199';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890226';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497685';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359148';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733242';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100089559';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733261';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733265';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890240';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890250';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384808';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089775';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890261';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733267';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497686';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100082247';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999469';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733278';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384820';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999482';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890277';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384832';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890279';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384838';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384840';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384844';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999526';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384846';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100089560';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384862';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384902';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999570';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999590';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497687';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384910';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384922';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384934';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100082248';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101384942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089854';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089860';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890298';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089863';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890301';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497688';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497689';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101385069';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089872';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089884';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890303';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890313';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890322';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497690';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101385089';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999594';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101385095';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101385113';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089887';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089896';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089897';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890326';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089900';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089902';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890341';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999615';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999625';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497691';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497692';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999630';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890343';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890355';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999641';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999647';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890358';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890375';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089922';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890377';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089937';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089941';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089949';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999654';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999659';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999661';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497693';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999667';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497694';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497695';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100082249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733338';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101385128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733342';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733346';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733379';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497697';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100089561';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890452';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101089964';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090014';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359256';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359355';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359362';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090026';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359366';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359372';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733384';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090038';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890460';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359400';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359414';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359422';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733403';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733409';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999688';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999693';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090073';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090080';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890477';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090091';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733411';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733436';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101359437';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497698';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100497699';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100089562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100082251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100890491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733447';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101385185';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101385216';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100999699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA100733456';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090116';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:38' WHERE product_id = 'CMA101090125';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733467';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385253';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385264';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733479';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090128';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890519';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359446';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359454';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359463';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090136';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359473';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090146';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890526';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090147';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090148';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999713';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999756';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090150';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090165';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733557';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100497700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733597';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100497701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733613';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090179';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090193';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090197';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090210';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090213';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090224';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100497702';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090227';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100082252';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733638';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359523';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359574';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890535';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890538';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999763';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999766';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999768';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999770';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385374';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999772';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999785';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999788';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999796';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999798';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999816';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999824';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999830';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999843';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100089563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999852';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890548';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890551';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999854';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999864';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359584';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999868';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999917';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359621';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359628';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385393';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385432';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890557';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890566';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890576';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385457';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890579';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890592';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359640';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890602';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359646';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890628';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890640';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090239';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890655';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385519';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385528';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890670';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890688';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385543';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733639';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100082253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733647';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999970';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100089564';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100999990';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359798';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359825';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000039';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090285';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733706';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890706';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890715';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890724';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385595';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890730';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385694';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000064';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359845';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890735';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100089565';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100497703';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090322';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101080070';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000084';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100082254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733709';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890746';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100082255';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385700';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000139';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100082257';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385709';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101385711';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359859';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359879';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000153';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000204';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000208';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101000217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733752';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890782';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890797';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090341';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090353';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890807';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359888';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359899';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890814';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359910';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359920';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359926';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733762';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359939';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359960';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359967';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100890822';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101359983';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA100733780';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360016';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090356';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101090380';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:39' WHERE product_id = 'CMA101360022';
COMMIT;
