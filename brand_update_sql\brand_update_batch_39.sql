BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826220';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410522';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA101410784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:08' WHERE product_id = 'CMA100826699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101410996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411011';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100826987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101411993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA100827940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:09' WHERE product_id = 'CMA101412347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100827983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412522';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101412991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA101413498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:10' WHERE product_id = 'CMA100828759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100828991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101413957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA100829630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:11' WHERE product_id = 'CMA101414932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414971';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101414976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829708';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415147';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829850';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829911';
COMMIT;
