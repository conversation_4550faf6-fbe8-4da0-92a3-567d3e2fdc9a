BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100793978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101380999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100794990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA101381604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:45' WHERE product_id = 'CMA100795132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101381953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100795991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382497';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101382972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796865';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100796974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100797009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100797024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA101383355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:46' WHERE product_id = 'CMA100797042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797734';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100797950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101383992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA101384294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:47' WHERE product_id = 'CMA100798759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384371';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100798912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384554';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101384998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100799987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800175';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100800980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100801010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA101385699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100801013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100801127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100801143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:48' WHERE product_id = 'CMA100801156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA100801233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:49' WHERE product_id = 'CMA101385783';
COMMIT;
