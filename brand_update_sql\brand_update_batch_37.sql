BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA101398917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:59' WHERE product_id = 'CMA100817939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101398928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101398948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101398956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101398964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101398972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100817999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101398982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818371';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100818982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100819007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA100819088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:00' WHERE product_id = 'CMA101399949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101399951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101399952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101399962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101399999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA100819396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:01' WHERE product_id = 'CMA101400325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819762';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101400997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100819989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA101401539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:02' WHERE product_id = 'CMA100820343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820693';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101401992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820707';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402724';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100820999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821014';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA101402922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:03' WHERE product_id = 'CMA100821052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101402925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101402942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101402948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101402979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101402985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101402990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101402997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403240';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821522';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101403916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA100821827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:04' WHERE product_id = 'CMA101404161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA101404179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821853';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:05' WHERE product_id = 'CMA100821860';
COMMIT;
