BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100109998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708321';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100133932';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100220322';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580871';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100133933';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100087856';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100087857';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100133934';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876095';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100044497';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100493950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100079502';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580872';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100032676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708363';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708372';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100220323';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708415';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100110000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708431';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876105';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876121';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100133935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708438';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100032677';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580891';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100110001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100079503';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100044498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708473';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100032678';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100493951';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100220324';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100032679';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100220325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100079504';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100087858';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100044499';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100032680';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100493952';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100133936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708477';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876122';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100032681';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580894';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100133937';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100110002';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876132';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580896';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100493953';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876155';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876161';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876162';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100079505';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876170';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708515';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100110035';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100220326';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100220327';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876177';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876187';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708517';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876191';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100110036';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708558';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580898';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876198';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708579';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876201';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876228';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876231';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876232';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100044500';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100032682';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100044501';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100044502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100079506';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100493954';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100087859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708582';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100044503';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580900';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708585';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580901';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876252';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100110037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100133939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708635';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100220328';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100493955';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876258';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876297';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100580903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708637';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100708642';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:54' WHERE product_id = 'CMA100876315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079507';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876318';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110038';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493956';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220329';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087860';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032683';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708644';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493957';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876328';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876338';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876349';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876350';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708712';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708755';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708777';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708781';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708790';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708796';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876375';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876389';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876390';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110039';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133943';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583139';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079508';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133944';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708829';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032685';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044505';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220330';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133945';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133947';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493960';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079509';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708841';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583147';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583151';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110040';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583216';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583220';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876411';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583225';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708853';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876443';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583226';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708875';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876455';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110042';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708894';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133948';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876464';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876466';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876475';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220332';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708939';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110043';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876476';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876479';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876480';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876482';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876484';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876581';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493961';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133950';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100708954';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220333';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583227';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709052';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133951';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087863';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709130';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032687';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110044';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709163';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876602';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876658';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709263';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709271';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709273';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709300';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044616';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079513';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110046';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044617';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583228';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876664';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709308';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133953';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709343';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876799';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583229';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100133954';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110047';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583230';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032691';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709361';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709385';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709394';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876823';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044618';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100583237';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876829';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876836';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876841';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876846';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087865';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876866';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709404';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100134132';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709457';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220337';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079515';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044620';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220338';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709492';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709504';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876871';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100220339';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876883';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876885';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876888';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100493965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100709505';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100110048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100079516';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100032692';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100044621';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100134137';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100087867';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:55' WHERE product_id = 'CMA100876895';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220553';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876903';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876913';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583409';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876916';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876920';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876922';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876923';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876925';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044622';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110049';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220554';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493966';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583413';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032693';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876934';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583414';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876944';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100087868';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100079517';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100079518';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134140';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134142';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110050';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220555';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709520';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044623';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876955';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493967';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044624';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100079649';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220556';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110053';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493968';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100087869';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709570';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493969';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876964';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876974';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134145';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493970';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044626';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583418';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134146';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220557';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032694';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583419';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709604';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100876976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709611';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044627';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877003';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100087870';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877008';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709616';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709626';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044628';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709645';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134149';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877029';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877032';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110054';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100079650';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709661';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583425';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877045';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032696';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032697';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134152';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709672';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220560';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134154';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877048';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877070';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110055';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100079651';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583431';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220561';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134155';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493972';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877075';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220562';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877093';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044630';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709741';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044631';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709748';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877128';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877143';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877150';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877152';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877157';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877161';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100088028';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134157';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583435';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100079652';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110224';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877170';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877177';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044632';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709812';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877186';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709838';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709839';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709848';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877201';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709879';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220564';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877206';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877214';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877231';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044633';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100134303';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877237';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100088029';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877250';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709921';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709931';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709959';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100583436';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032817';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100493974';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100044634';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100709961';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100110227';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100088030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710033';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100032819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710037';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710039';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710043';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100220565';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877283';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100877296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100710059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:56' WHERE product_id = 'CMA100079653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710067';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877305';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877335';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877344';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100220566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710087';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100220567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710097';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710104';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100079654';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710116';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710118';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100493975';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877358';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877359';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877362';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877371';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583437';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877374';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134305';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877375';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877393';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877403';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100220568';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710151';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877416';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877430';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877457';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877465';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877474';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877476';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877482';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100088031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100079655';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494134';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100032820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710173';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134306';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877490';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877496';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710214';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710216';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877516';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710220';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710223';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044637';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583438';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100032821';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583439';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877519';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100220569';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877532';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100220570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877541';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100220571';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100079656';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494135';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101503123';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100088032';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134307';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583441';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710232';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134309';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877573';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877580';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710288';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710295';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877599';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110230';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044638';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100079657';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100032822';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877600';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134310';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877622';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110231';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583616';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877625';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044639';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110233';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877635';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044640';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101503218';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877651';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877653';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504029';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877654';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504129';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877656';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100032823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710304';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110234';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504202';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494137';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877660';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134311';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134312';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877674';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583618';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110236';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044642';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877687';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877690';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504353';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877692';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710314';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877702';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877711';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044788';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100088033';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044789';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100088034';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710329';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100079658';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504566';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100032824';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877712';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877732';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877736';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877739';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877744';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877746';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877748';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134315';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494138';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583631';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710472';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877757';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583632';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710492';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710539';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710551';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710570';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710591';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504876';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710633';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710665';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710667';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100583636';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101504955';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101505026';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101505043';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100088035';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100044792';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101505051';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100877762';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101505062';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100494141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710671';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100110239';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710689';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101505066';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA101505073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100710701';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100134318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:57' WHERE product_id = 'CMA100079659';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100134319';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110240';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505088';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505104';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100088036';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710741';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710751';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505173';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583637';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505176';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505241';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877768';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100088037';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583640';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583641';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505741';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583642';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044793';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032825';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505758';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032826';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100134321';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505930';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505940';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710753';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101505945';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877776';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877785';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877789';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877792';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877802';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877803';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100134322';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583671';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710767';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110241';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583672';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079660';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710781';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710784';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494144';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877806';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877814';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877815';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877828';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877832';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506158';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877835';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877859';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710826';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110242';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100088038';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877873';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583673';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506254';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494145';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877878';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877879';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583676';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877881';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583677';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583678';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079661';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877891';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506276';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877896';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506289';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044795';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100134323';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506412';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877928';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032829';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494146';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710830';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032830';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583681';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079662';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583682';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100877940';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878043';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506415';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494147';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506467';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878047';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878052';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044796';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100134325';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878054';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079663';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506497';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506509';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110244';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100088039';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100134326';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079664';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583683';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494148';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878180';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878208';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878216';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878218';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044797';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878229';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878231';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878232';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878235';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710849';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710860';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710862';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506514';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100583687';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878245';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044798';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506536';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044799';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878250';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100326040';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494149';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079665';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110245';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878272';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506555';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878275';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100088040';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506610';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506628';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878277';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506638';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878280';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506652';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878286';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878297';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878300';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878302';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878306';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878309';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100326041';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110247';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878311';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878326';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067145';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878329';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878333';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044800';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878337';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067183';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878342';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878448';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878450';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100878452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067283';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100710897';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506746';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506759';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044802';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044803';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067340';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506773';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067356';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067396';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506807';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067413';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506845';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100494150';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067464';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067472';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067480';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067487';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032833';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100326042';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110248';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044804';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100326044';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110249';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506865';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506876';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067501';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044805';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079667';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506919';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506930';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101506941';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110250';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044806';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100088041';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044807';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101507605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100079668';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA101067533';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100110251';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044808';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044809';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044810';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100088042';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100044811';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:58' WHERE product_id = 'CMA100032834';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100032835';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067539';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100032836';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110252';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067596';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100032837';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044812';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044813';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044814';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878456';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044815';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044816';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044846';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710900';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044848';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079669';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110254';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494151';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507628';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110256';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110257';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067656';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878554';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100032838';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088043';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067696';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110258';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088044';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067721';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067734';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067748';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044849';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067756';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878567';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878572';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079670';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044850';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044851';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507666';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878579';
COMMIT;
