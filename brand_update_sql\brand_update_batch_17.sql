BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727186';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886899';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375608';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886940';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350760';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350779';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350797';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375676';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375703';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727216';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886946';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727223';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082909';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082915';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082920';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350808';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886971';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100886995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727233';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100887006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727262';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375727';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101375731';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082928';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101082994';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101083006';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101083014';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350832';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727265';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727311';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350859';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100081875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994717';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727318';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994774';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496847';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994812';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727354';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727369';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350903';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100089174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727371';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994854';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101083024';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100994858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727447';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100496848';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727456';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA100727466';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350947';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:27' WHERE product_id = 'CMA101350955';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496958';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887010';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101350962';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887035';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101350985';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081877';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083032';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375777';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887039';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375789';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887049';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887061';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101350988';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887071';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351021';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375797';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887076';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351030';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351044';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351049';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351052';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727472';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727476';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083060';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351061';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351098';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375835';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375843';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083083';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351108';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727496';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083099';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351126';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351139';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887118';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351141';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887125';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887130';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887139';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081878';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100089175';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375891';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375922';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083112';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083173';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083174';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496959';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887149';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083177';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887157';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083179';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887169';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083182';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351157';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727497';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351160';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351165';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887192';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351168';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887198';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351171';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083193';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887205';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727543';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351173';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351188';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100994863';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351189';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100994901';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351190';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887218';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351191';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887225';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496960';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887230';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727563';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887235';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083223';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375936';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100994905';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083236';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100994957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727581';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083251';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100994964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083263';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727594';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083271';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727607';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887238';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887252';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083278';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496961';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887256';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083284';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083288';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101375994';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083297';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376005';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351213';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376016';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351223';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351227';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083302';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351244';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083335';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351249';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083344';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351255';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351259';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727639';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351283';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727646';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100994967';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100089176';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496962';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995005';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376062';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887276';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887305';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081879';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351314';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496963';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083357';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083370';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351343';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083376';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100089177';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376067';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376078';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351365';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995011';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887313';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887372';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887382';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351376';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995030';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083379';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083385';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351387';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351414';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887391';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887398';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887403';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887412';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887421';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995035';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887430';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887439';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887449';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995045';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887452';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351435';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351447';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351452';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887461';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351480';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376086';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351496';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351516';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351549';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887496';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351554';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083396';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083404';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496964';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376113';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351556';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081880';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887541';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100089178';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496965';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351578';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995092';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995119';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995132';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995137';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995142';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995152';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083412';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083428';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083434';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995160';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083438';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083444';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887548';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083449';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995225';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727758';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376176';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376189';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995241';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887564';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995250';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887588';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995267';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727762';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995273';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995277';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995285';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083458';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376203';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376222';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376243';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376254';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995289';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081882';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351600';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351656';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351677';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351706';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100089179';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351710';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727839';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351737';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351750';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351754';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083469';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083498';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083499';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083506';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496967';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351771';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351789';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995308';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351798';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995350';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995352';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100089180';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727849';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376357';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727892';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351805';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083519';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083534';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083537';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083552';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351819';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995354';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995368';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727896';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727904';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101376389';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887604';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995371';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100081884';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496968';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100089181';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995390';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351843';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083558';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995428';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351852';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083563';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887652';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100496970';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995429';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995436';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727908';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995444';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995458';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995460';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351868';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995465';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083569';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100995468';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083574';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100887663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA100727941';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083586';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083594';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083596';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351883';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083599';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101351928';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:28' WHERE product_id = 'CMA101083602';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995483';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083607';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995487';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083610';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995496';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887673';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376487';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083613';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083618';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100727951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100727957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100727960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100727967';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887705';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100727970';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995518';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995535';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995548';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083639';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083653';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887733';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887761';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728087';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100081999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728112';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995558';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995567';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100089182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728115';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083724';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995585';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995599';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376513';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376521';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728132';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887797';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728133';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887827';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887848';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995614';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100089183';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887850';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083739';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728314';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728315';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083790';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100082000';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376606';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376630';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083862';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887896';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887903';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887905';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728350';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728354';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887916';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728368';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887924';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887935';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887937';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887941';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083864';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351931';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100082001';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351956';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083870';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496972';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351965';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351969';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083886';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100089184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995636';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351975';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351976';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083900';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083910';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995655';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083912';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995656';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083915';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887947';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083917';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995657';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887979';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887983';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887987';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083936';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887994';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100887999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888008';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888011';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083948';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351985';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888021';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888023';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888029';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101083993';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101351997';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888034';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888042';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995707';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995711';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376705';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376738';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084001';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352013';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995718';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728392';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728400';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084041';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728403';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352065';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995753';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352068';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352070';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995767';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084064';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352075';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888059';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728404';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995771';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995790';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084075';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084085';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728457';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728461';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728466';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728476';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352096';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352101';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352107';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995821';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352113';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995830';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352118';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728480';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352130';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888093';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888103';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728506';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888114';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888120';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352145';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995847';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888131';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995872';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728518';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888166';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888172';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352159';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352176';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995926';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352182';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888187';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888195';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888198';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352200';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888206';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352204';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995931';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995966';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352211';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100082003';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352230';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888212';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100089185';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496973';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352233';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084096';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888227';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888235';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352240';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352257';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084152';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888243';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084161';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084170';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728566';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084178';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084229';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376745';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084230';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352263';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352268';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352271';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352274';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376785';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084250';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084283';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084300';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084309';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352277';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100082004';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084346';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352296';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352312';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084348';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084357';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084360';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352320';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084370';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084388';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352343';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352350';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352367';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352372';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084457';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084482';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352386';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352393';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352399';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995973';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376832';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376866';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084503';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084563';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100082005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728639';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496975';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100089186';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496976';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100082006';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496977';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100888251';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496978';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376904';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101376908';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100995982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100082007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100728703';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101377073';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101084569';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101377091';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA100496979';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:29' WHERE product_id = 'CMA101352406';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084664';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082008';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728709';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728729';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728745';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377363';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377371';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352471';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352478';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352486';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888277';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888299';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100995993';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996004';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084676';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084678';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084684';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084687';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728774';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082009';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728792';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100496980';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377406';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377424';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728840';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728842';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084703';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084724';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084730';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084733';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100496981';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100496982';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084740';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728862';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352500';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352511';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728866';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728870';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084774';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084792';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084798';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728873';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352520';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352529';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352539';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084868';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888320';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996023';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728882';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728895';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728898';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888392';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888397';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996028';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888401';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888409';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996035';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377450';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888413';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100496983';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089189';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082010';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888434';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728942';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996072';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352542';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996078';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728949';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996090';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377468';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100496984';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084873';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082011';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996096';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996105';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996113';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352570';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888459';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996116';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996152';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888503';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728987';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888511';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377504';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377517';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377527';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888529';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888539';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728995';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888547';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100728998';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888557';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888562';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352611';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352626';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888566';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996178';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089191';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377532';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377558';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101084896';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100496985';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085025';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352637';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352646';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089192';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352654';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352659';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377571';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377584';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352666';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352702';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888590';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352709';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352713';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352719';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352721';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352725';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352730';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082013';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377609';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085047';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729125';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085089';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085101';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100497201';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377624';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996251';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729204';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085107';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729210';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729218';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085132';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996270';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085157';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729236';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729254';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996276';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996281';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729272';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085240';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377642';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377692';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377702';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996296';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377713';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729279';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996314';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996330';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729286';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729304';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352945';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996332';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996344';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996349';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101352953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996368';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377738';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996380';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377751';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082014';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377786';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377813';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996385';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729317';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101353031';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100089328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729394';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996415';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996433';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996440';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377833';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996447';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377838';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377844';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100497202';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101377854';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100497203';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085272';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996452';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085294';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085307';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085318';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085320';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085330';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996483';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996491';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996496';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888623';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888649';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888653';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888657';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888660';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888662';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996499';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085341';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085396';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101085402';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996502';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888665';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888670';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888672';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888680';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888682';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888684';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888685';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100888687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100729436';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100497204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100082015';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100996523';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA101353122';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:30' WHERE product_id = 'CMA100497205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996535';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729502';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101377860';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101377913';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729504';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101377926';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101377936';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101377946';
COMMIT;
