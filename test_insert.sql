-- 测试INSERT语句（包含INSERT INTO和INSERT OR REPLACE）
INSERT INTO products202503 (product_id, model, brand_id, price_key, stock_key, datasheet_url, image_list, parameters_key, description, updated_at, rohs, summarizer, moq, spq, weight, url) VALUES ('CMA100242091', 'Test Model 1', 'BRAND001', 'PRICE001', 'STOCK001', 'https://datasheet.lcsc.com/lcsc/2210311700_Test1.pdf', '[]', 'PARAM001', 'Test Description 1', '2025-01-01 00:00:00', 1, NULL, 1, 1, 0.1, 'https://test1.com');

INSERT OR REPLACE INTO products202503 (product_id, model, brand_id, price_key, stock_key, datasheet_url, image_list, parameters_key, description, updated_at, rohs, summarizer, moq, spq, weight, url) VALUES ('CMA100840887', 'Test Model 2', 'BRAND002', 'PRICE002', 'STOCK002', 'https://datasheet.lcsc.com/lcsc/2410010101_Test2.pdf', '[]', 'PARAM002', 'Test Description 2', '2025-01-01 00:00:00', 1, NULL, 1, 1, 0.2, 'https://test2.com');

INSERT INTO products202503 (product_id, model, brand_id, price_key, stock_key, datasheet_url, image_list, parameters_key, description, updated_at, rohs, summarizer, moq, spq, weight, url) VALUES ('CMA100999999', 'Test Model 3', 'BRAND003', 'PRICE003', 'STOCK003', 'https://datasheet.lcsc.com/lcsc/test_with_quotes_''_and_commas.pdf', '[]', 'PARAM003', 'Test Description with ''quotes'' and, commas', '2025-01-01 00:00:00', 1, NULL, 1, 1, 0.3, 'https://test3.com');

INSERT OR REPLACE INTO products202503 (product_id, model, brand_id, price_key, stock_key, datasheet_url, image_list, parameters_key, description, updated_at, rohs, summarizer, moq, spq, weight, url) VALUES ('CMA100555555', 'Test Model 4', 'BRAND004', 'PRICE004', 'STOCK004', 'https://datasheet.lcsc.com/lcsc/another_test.pdf', '[]', 'PARAM004', 'Another test description', '2025-01-01 00:00:00', 1, NULL, 1, 1, 0.4, 'https://test4.com');
