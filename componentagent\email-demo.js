/**
 * 邮件发送API示例
 * 
 * 展示如何使用邮件发送API和模板
 */

import { generateInquiryEmail, defaultInquiryItems } from './email-template.js';

/**
 * 使用本地Worker发送邮件
 * @param {string} apiKey - Resend API密钥
 * @param {Object} emailData - 邮件数据
 * @returns {Promise<Object>} 响应对象
 */
async function sendEmailWithLocalWorker(apiKey, emailData) {
  const response = await fetch('http://localhost:8787/api/send', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(emailData)
  });
  
  return await response.json();
}

/**
 * 使用部署的Worker发送邮件
 * @param {string} apiKey - Resend API密钥
 * @param {Object} emailData - 邮件数据
 * @returns {Promise<Object>} 响应对象
 */
async function sendEmailWithDeployedWorker(apiKey, emailData) {
  // 替换为您部署的Worker URL
  const workerUrl = 'https://your-worker.your-subdomain.workers.dev/api/send';
  
  const response = await fetch(workerUrl, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(emailData)
  });
  
  return await response.json();
}

/**
 * 直接使用Resend API发送邮件
 * @param {string} apiKey - Resend API密钥
 * @param {Object} emailData - 邮件数据
 * @returns {Promise<Object>} 响应对象
 */
async function sendEmailWithResendAPI(apiKey, emailData) {
  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(emailData)
  });
  
  return await response.json();
}

/**
 * 主函数 - 演示发送邮件
 */
async function main() {
  // 配置信息
  const apiKey = 'YOUR_RESEND_API_KEY'; // 替换为您的Resend API密钥
  const from = 'service <<EMAIL>>'; // 发件人信息
  const to = ['<EMAIL>']; // 收件人邮箱
  const cc = ['<EMAIL>']; // 抄送人邮箱
  const subject = '测试邮件[RFQ' + new Date().toISOString().slice(0, 10).replace(/-/g, '') + Math.floor(Math.random() * 10000).toString().padStart(4, '0') + ']';
  
  // 生成邮件内容
  const html = generateInquiryEmail(defaultInquiryItems);
  
  // 准备邮件数据
  const emailData = {
    from,
    to,
    cc,
    subject,
    html
  };
  
  try {
    console.log('准备发送邮件...');
    
    // 选择一种方式发送邮件
    // const result = await sendEmailWithLocalWorker(apiKey, emailData);
    // const result = await sendEmailWithDeployedWorker(apiKey, emailData);
    const result = await sendEmailWithResendAPI(apiKey, emailData);
    
    console.log('邮件发送结果:', result);
  } catch (error) {
    console.error('发送邮件时出错:', error);
  }
}

// 如果在浏览器环境中运行此脚本，可以通过按钮触发发送
if (typeof window !== 'undefined') {
  window.sendTestEmail = main;
}

// 如果在Node.js环境中运行此脚本，直接执行main函数
if (typeof process !== 'undefined' && process.versions && process.versions.node) {
  main().catch(console.error);
}

export { sendEmailWithLocalWorker, sendEmailWithDeployedWorker, sendEmailWithResendAPI };