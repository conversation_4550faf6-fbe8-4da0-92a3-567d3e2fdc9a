# 存储服务关联关系指南

本文档详细说明了不同存储服务之间的关联关系，以及如何使用唯一标识符在各个服务之间建立连接。

## 核心概念

在我们的系统中，`product_id` 作为主要的关联键，用于连接不同存储服务中的相关数据。

## 存储服务关联关系

### D1 数据库
- 作为主数据存储，包含产品的基本信息
- 使用 `product_id` 作为主键
- 存储产品名称、描述、类别等基本信息

### KV 存储

#### PRICE_DATA
- 键格式：`product:{product_id}:price`
- 存储产品的实时价格信息
- 便于快速查询和更新

#### STOCK_DATA
- 键格式：`product:{product_id}:stock`
- 存储产品的实时库存信息
- 支持高频更新操作

### R2 存储桶

#### DATASHEETS
- 文件路径格式：`{product_id}/spec.pdf`
- 存储产品规格说明书
- 支持版本控制

#### PRODUCT_IMAGES
- 文件路径格式：`{product_id}/{image_type}.jpg`
- image_type 可以是：main、thumbnail、detail-1 等
- 存储产品相关的图片资源

### Vectorize 索引

#### PRODUCTS_INDEX
- 向量 ID 格式：`product:{product_id}`
- 存储产品描述的向量表示
- 用于相似产品搜索

## 数据一致性维护

### 创建新产品流程
1. 在 D1 数据库中创建产品记录，获取 product_id
2. 在 KV 存储中初始化价格和库存数据
3. 上传产品图片到 R2 存储桶
4. 生成并存储产品描述向量

### 更新产品信息流程
1. 更新 D1 数据库中的基本信息
2. 根据需要更新 KV 中的价格或库存
3. 如有新图片，上传到 R2 存储桶
4. 如果描述发生变化，更新向量索引

## 查询示例

### 获取完整产品信息
```typescript
async function getProductDetails(productId: string) {
  // 获取基本信息
  const basicInfo = await DB
    .prepare('SELECT * FROM products WHERE product_id = ?')
    .bind(productId)
    .first();

  // 获取价格
  const price = await PRICE_DATA.get(`product:${productId}:price`);

  // 获取库存
  const stock = await STOCK_DATA.get(`product:${productId}:stock`);

  // 获取图片 URL
  const mainImageUrl = await PRODUCT_IMAGES.get(`${productId}/main.jpg`);

  return {
    ...basicInfo,
    price,
    stock,
    mainImageUrl
  };
}
```

### 查找相似产品
```typescript
async function findSimilarProducts(productId: string, limit: number = 5) {
  // 获取产品向量
  const vector = await PRODUCTS_INDEX.query({
    vector: `product:${productId}`,
    topK: limit
  });

  // 获取相似产品的详细信息
  const similarProducts = await Promise.all(
    vector.matches.map(match => getProductDetails(match.id))
  );

  return similarProducts;
}
```

## 最佳实践

1. 使用事务确保数据一致性
   - 在创建或更新产品时，使用事务确保所有存储服务的更新都成功
   - 如果任何步骤失败，回滚所有更改

2. 实现重试机制
   - 对于可能失败的操作实现重试逻辑
   - 使用指数退避策略

3. 缓存策略
   - 对频繁访问的数据实现适当的缓存
   - 使用 KV 存储作为缓存层

4. 错误处理
   - 实现完善的错误处理机制
   - 记录详细的错误日志
   - 设置监控告警

5. 定期数据同步
   - 实现定期检查机制，确保各存储服务之间的数据一致性
   - 自动修复不一致的数据

## 监控建议

1. 设置关键指标监控
   - 数据一致性检查
   - 存储服务响应时间
   - 错误率统计

2. 建立告警机制
   - 数据不一致告警
   - 服务性能下降告警
   - 存储容量告警

## 注意事项

1. 定期备份重要数据
2. 监控存储使用量和成本
3. 遵循最小权限原则
4. 定期审查和更新访问权限
5. 确保所有存储服务的配置都在版本控制中