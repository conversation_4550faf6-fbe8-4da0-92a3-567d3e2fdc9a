BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101459998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858607';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460402';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100858995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460588';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101460987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859434';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA101461178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:43' WHERE product_id = 'CMA100859480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859493';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461464';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859775';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461700';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100859997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461752';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860118';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101461994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462147';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462247';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860473';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101462996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463162';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860765';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA101463458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:44' WHERE product_id = 'CMA100860838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463573';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463590';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860934';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100860998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861035';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463734';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861197';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101463988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861371';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861636';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464734';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100861967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862055';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101464993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA101465174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:45' WHERE product_id = 'CMA100862162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862608';
COMMIT;
