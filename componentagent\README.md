# 邮件发送 Cloudflare Worker

这是一个基于Cloudflare Workers的邮件发送API，使用Resend API作为底层邮件发送服务。

## 功能特点

- 提供RESTful API接口发送邮件
- 支持设置发件人、收件人、抄送人、主题和HTML内容
- 支持CORS，可以从前端应用调用
- 提供邮件模板生成功能，特别是询价表格邮件

## 文件结构

- `email-worker.js` - Cloudflare Worker主文件，处理API请求
- `email-template.js` - 邮件模板生成器，用于生成HTML邮件内容
- `email-demo.js` - 示例代码，展示如何使用API

## 部署步骤

1. 安装Cloudflare Workers CLI工具（Wrangler）

```bash
npm install -g wrangler
```

2. 登录到您的Cloudflare账户

```bash
wrangler login
```

3. 创建一个新的Worker项目

```bash
mkdir email-worker
cd email-worker
wrangler init
```

4. 将`email-worker.js`的内容复制到`src/index.js`

5. 配置`wrangler.toml`文件

```toml
name = "email-worker"
main = "src/index.js"
compatibility_date = "2023-10-02"

[vars]
# 可选：如果您想在Worker中验证API密钥
# RESEND_API_KEY = "your_resend_api_key"
```

6. 部署Worker

```bash
wrangler publish
```

## 使用方法

### API端点

- **POST** `/api/send` - 发送邮件

### 请求格式

```json
{
  "from": "发件人名称 <<EMAIL>>",
  "to": ["<EMAIL>"],
  "cc": ["<EMAIL>"],  // 可选
  "bcc": ["<EMAIL>"],  // 可选
  "subject": "邮件主题",
  "html": "<p>邮件HTML内容</p>",
  "reply_to": "<EMAIL>"  // 可选
}
```

### 请求头

- `Content-Type: application/json`
- `Authorization: Bearer YOUR_RESEND_API_KEY`

### 示例请求

```javascript
const response = await fetch('https://your-worker.your-subdomain.workers.dev/api/send', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer re_123456789',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    from: 'Your Name <<EMAIL>>',
    to: ['<EMAIL>'],
    subject: '测试邮件',
    html: '<p>这是一封测试邮件</p>'
  })
});

const result = await response.json();
console.log(result);
```

## 使用邮件模板

邮件模板可以帮助您生成格式化的HTML邮件内容。例如，使用询价表格模板：

```javascript
import { generateInquiryEmail, defaultInquiryItems } from './email-template.js';

// 使用默认项目生成邮件内容
const html = generateInquiryEmail(defaultInquiryItems);

// 或者使用自定义项目
const customItems = [
  { no: 1, description: "自定义产品1", quantity: 10 },
  { no: 2, description: "自定义产品2", quantity: 20 }
];
const customHtml = generateInquiryEmail(customItems, "尊敬的客户", "销售部");
```

## 本地开发

1. 克隆此仓库
2. 安装依赖：`npm install`
3. 本地运行Worker：`wrangler dev`
4. 访问 `http://localhost:8787` 进行测试

## 注意事项

- 您需要一个有效的Resend API密钥才能发送邮件
- 确保您的发件人邮箱已在Resend平台验证
- Cloudflare Workers有执行时间限制，通常为50ms（付费计划可达30s）

## 许可证

MIT 