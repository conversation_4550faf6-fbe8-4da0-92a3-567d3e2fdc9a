BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878592';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878597';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494152';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110260';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507676';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326045';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507695';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878607';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507789';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878622';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878626';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878627';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067951';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878629';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878634';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878638';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710921';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878642';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878655';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878656';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878657';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878658';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710931';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044852';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067974';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110261';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878661';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100032839';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079671';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067978';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101067983';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878690';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878695';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878702';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044854';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878706';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878709';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044855';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494153';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878712';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044856';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507790';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507843';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326047';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710939';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088046';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494154';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326049';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507864';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101068060';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044857';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878713';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878716';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079672';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878729';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326050';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878732';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878735';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878741';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507870';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326052';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878745';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878748';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507883';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878765';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507910';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507913';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079673';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044858';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507964';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507985';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494155';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507989';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100032840';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507991';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101507999';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100710998';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110262';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088048';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326055';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508000';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878766';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878787';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110265';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878789';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878793';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494156';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878795';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079674';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088049';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101068354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711015';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508007';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044859';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711048';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711050';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101068403';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494157';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326058';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878824';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878829';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878832';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711055';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711066';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326060';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711075';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508055';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711105';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878838';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878842';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878844';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100494158';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711134';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711139';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711143';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101508089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711147';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711191';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101068408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711193';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100032841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100079677';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878850';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101068457';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878853';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878856';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101509876';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326784';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100044860';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101509885';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326785';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711200';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326786';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878857';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100326787';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA101510085';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878860';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110267';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100088051';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100110268';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878862';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100878868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:59' WHERE product_id = 'CMA100711207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711213';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711216';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711224';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510176';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878891';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878897';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878904';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878908';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878911';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878912';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110272';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878914';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494159';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510190';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878917';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878923';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494160';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711227';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326788';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068496';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100088052';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711246';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510223';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100088053';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510251';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510252';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878927';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878938';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711254';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100079678';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100032842';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711306';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044861';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068512';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878942';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878945';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100088054';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068522';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326789';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711318';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711327';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044862';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494162';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510293';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878947';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510296';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878960';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878964';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878965';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878967';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878969';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711330';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878972';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711332';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510297';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878974';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878996';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110426';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100032843';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100878999';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879001';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879002';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510311';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711341';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510343';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100079679';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879009';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879021';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110428';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326792';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068539';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510434';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044863';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326794';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711360';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101510505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711370';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879041';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879059';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100088055';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101511503';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494321';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044865';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711375';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326798';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100032844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100079680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711424';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068540';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100088056';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711430';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326799';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068558';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110431';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494322';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711436';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711440';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101511941';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100079681';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100032845';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711449';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110433';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068576';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068595';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711459';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711461';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100088057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100079682';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044868';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711467';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711471';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110435';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879111';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711476';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100032846';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100088280';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068603';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068612';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100079683';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100494324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711496';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110436';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100711498';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326805';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068633';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068641';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068650';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044870';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110438';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044871';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068651';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068664';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068668';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA101068672';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879145';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879151';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100879171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100079684';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100044872';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110440';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100326806';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:00' WHERE product_id = 'CMA100110441';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032847';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879184';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879208';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879221';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879224';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879231';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100088281';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100494325';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879256';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068678';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068703';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032848';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326807';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100088282';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068708';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032849';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711552';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326968';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100110443';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326969';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079685';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879271';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045100';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879278';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045101';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879306';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879356';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045102';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100494326';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100494327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711577';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032850';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879365';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879375';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326970';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326971';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068759';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068772';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879387';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100088283';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879419';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879425';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100110653';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711678';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045103';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879430';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711687';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879439';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711702';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079687';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045104';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045105';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100088284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079688';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879509';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068801';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068823';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068849';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068902';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068906';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068912';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068920';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879510';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045106';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045107';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079689';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100494328';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326974';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711709';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326978';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100110654';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711731';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100110655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079690';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068925';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068959';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100494329';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068975';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032852';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101068977';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069007';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069011';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100088285';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069018';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711749';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032853';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879512';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879543';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879545';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879547';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879549';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069071';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326980';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069081';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069089';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045108';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069103';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326981';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879553';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879578';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045110';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879580';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045111';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711770';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079691';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326984';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032854';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879590';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711816';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879609';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711869';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079693';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711894';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100326987';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069300';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032855';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100494330';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100110658';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032856';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100088286';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711908';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711972';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879677';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711974';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879712';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879742';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879751';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100045112';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879753';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100494331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100711977';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100032857';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100110659';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712075';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879759';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100088287';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879806';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100879812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100079694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712076';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712170';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA100712172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:01' WHERE product_id = 'CMA101069408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079695';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494332';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100110660';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100326988';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712186';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494333';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494334';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045113';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494335';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879815';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045114';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045115';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712204';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712235';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069421';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712301';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088290';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494337';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100110662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712307';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069427';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712316';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712403';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494338';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045117';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100326992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712412';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045118';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712590';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069515';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100326994';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879862';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032859';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100326995';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079697';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069529';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045119';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088291';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069539';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088292';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494340';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045120';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088293';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100326998';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079698';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069549';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100110664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712634';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079699';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100110666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712667';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712701';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088294';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879890';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879904';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494342';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712715';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100326999';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327000';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712734';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327004';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879906';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879922';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879927';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045122';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045123';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712747';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100110667';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079700';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088295';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045125';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327005';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088296';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879929';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879931';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879932';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712771';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069552';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100110668';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079701';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045372';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069605';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079702';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079703';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069626';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712781';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069647';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA101069654';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712789';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712799';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494344';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032863';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879940';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712809';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712845';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712856';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327010';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879971';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879973';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879976';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879984';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879985';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879988';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045373';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100110669';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088297';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712880';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088298';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327011';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032864';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879991';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100045375';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100879994';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100088299';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100032865';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100880004';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100494346';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100880008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100712905';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100880020';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327012';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100880028';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327013';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100880048';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100327014';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100880052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:02' WHERE product_id = 'CMA100079706';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069658';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069743';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110670';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880053';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880057';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880060';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880065';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880070';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880072';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100327016';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100327017';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100327018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100079707';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100079794';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045376';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880078';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880098';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712911';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088300';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069801';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069816';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712921';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880107';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880121';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880125';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069817';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880127';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880130';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712930';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045378';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880138';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880140';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880147';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069840';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100032866';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045379';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494348';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494349';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069873';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069887';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100032867';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069890';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100327022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712967';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110866';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100712991';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069893';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069929';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069945';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069966';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069973';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069975';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332027';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713003';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101069996';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070004';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070011';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088302';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713059';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100079795';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100032868';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713070';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713077';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713079';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880174';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494350';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713100';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713109';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713127';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713130';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713136';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070139';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880176';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880179';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110869';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045380';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494559';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110871';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070171';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088304';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100079796';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880201';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070200';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713141';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713165';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088305';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332035';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332037';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494560';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494561';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880203';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880208';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045381';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110872';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070246';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070258';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494562';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880216';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880243';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880248';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880261';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880270';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070263';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100032869';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070343';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045382';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070347';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110875';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070376';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088306';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100032870';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880276';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332039';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880281';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332040';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713176';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100079797';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045383';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100032871';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880290';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045384';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880294';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880300';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880302';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070413';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880308';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332041';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100713262';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100088308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100079798';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110876';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100032872';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100332042';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100494564';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA101070431';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100880310';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100045385';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:03' WHERE product_id = 'CMA100110878';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070438';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100494565';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880314';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880318';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880322';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880324';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880330';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880336';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100713271';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070452';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100045386';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100494566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100079799';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100110879';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070461';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070464';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100032873';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880338';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880407';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880408';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100088309';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880411';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100088310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100332043';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070467';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100332046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100079800';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100494567';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100045387';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070501';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100045389';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100088311';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100494568';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880417';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100079801';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100032874';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100032875';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070563';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100032876';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070574';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070580';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070593';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100110997';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100494569';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880422';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100332047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100079802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100713309';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100045390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100713331';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880434';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100033066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880447';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100332050';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880453';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100033067';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070598';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070604';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100110998';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100494570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880472';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100088312';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100033068';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100332051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100079803';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100332052';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100494571';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100045391';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880479';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880483';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880489';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880491';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880495';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100045392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100079804';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880501';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880522';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880525';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100713336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100079805';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880541';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100880550';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100088313';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100110999';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA100111000';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070607';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070619';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:04' WHERE product_id = 'CMA101070626';
COMMIT;
