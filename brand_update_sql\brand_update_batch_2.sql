BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100126620';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100489487';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100102305';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019235';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100091555';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490568';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100024889';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100199824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100362338';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100086243';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104808';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533048';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046579';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046585';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019236';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290371';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100077483';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290372';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100114425';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290373';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490569';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100086244';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290374';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019238';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019239';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100011860';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019240';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100126622';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100114435';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533049';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046589';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100199825';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100489488';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104816';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104833';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100091556';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104835';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104838';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100024890';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019243';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019244';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100114438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100362339';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100077484';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100024891';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290375';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104840';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100114439';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046610';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046623';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046624';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046626';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046648';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100102306';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490570';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490576';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046658';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046665';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100077485';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490578';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533051';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100116309';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100199826';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533052';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046698';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100116332';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100019245';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100126624';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100011861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100362340';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100116502';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100086245';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100024893';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100091557';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100490579';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290376';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290377';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101046754';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100533054';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290378';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100489489';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290379';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290380';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100290381';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104862';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA101104868';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:01' WHERE product_id = 'CMA100116632';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100489490';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100362341';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046794';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100086246';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102308';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019434';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100116634';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104875';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533055';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100116636';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100116796';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100091558';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100011862';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100490580';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100086247';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019435';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019436';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019437';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533056';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100362342';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100024894';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100490581';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100490592';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100117043';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046807';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100086248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100077486';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100199827';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019438';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019439';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019441';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102310';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100077487';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100117045';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100091631';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019442';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100024895';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290385';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100490596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100126627';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100199828';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100117051';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019443';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019444';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104880';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019446';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100489491';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046828';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102311';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100091632';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100011863';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533058';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100086249';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100126628';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100490771';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100011864';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100199829';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290386';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290387';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100362343';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019449';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046839';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104897';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104910';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100011865';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533059';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290388';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100117159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100077488';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533060';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102313';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533061';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100126629';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100011866';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019450';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019451';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100489492';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100086250';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100199830';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290389';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100091633';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533062';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533063';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100490773';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046859';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100362344';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102314';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100086251';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100126630';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290390';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100199831';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100489493';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100011867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100362346';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100126631';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019452';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019453';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019454';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019455';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019456';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019457';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104911';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533064';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104913';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100533065';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104915';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100117160';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290391';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100024896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100077489';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100126632';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019459';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019460';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101046868';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA101104918';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100024897';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100086252';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100091634';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100362348';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100019618';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100290392';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100117165';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100102315';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:02' WHERE product_id = 'CMA100490774';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101046888';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101046910';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100102316';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019619';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101046922';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126635';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100091635';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100533066';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100533067';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100024898';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100199832';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019620';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019621';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019622';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019623';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101046925';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019624';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126636';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019625';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019626';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019627';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019628';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019629';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019630';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100362349';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290393';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100489494';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100024899';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100086253';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100011868';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126638';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100117176';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100533068';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101046945';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019631';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100490779';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019632';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100091636';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100102318';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100011869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100077490';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104926';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101046948';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100024900';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290394';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100011870';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100091637';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100199833';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100490781';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019633';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100535536';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104934';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047027';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100077492';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100086254';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290396';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019634';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100490782';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019635';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290397';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019637';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126639';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104939';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100011871';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104943';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290398';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019639';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290399';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019640';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290400';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019641';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290401';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290402';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100102319';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100117182';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126640';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019642';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019643';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100489495';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019644';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100362350';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290403';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290404';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019646';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019647';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019648';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100011872';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019649';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100102320';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100490783';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100091638';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100199834';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019650';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100117338';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100535537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100077493';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100535538';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019651';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047057';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100489496';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100102321';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126641';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047076';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019652';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100091639';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104954';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104958';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104962';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104964';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019653';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104969';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100011873';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104974';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104982';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019654';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047115';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047124';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100362351';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047140';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104988';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047142';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100362352';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019656';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047143';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100535539';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290405';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019657';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290406';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019658';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047155';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101104999';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047185';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019659';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019660';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047205';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100019661';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290407';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105006';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105033';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047218';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105039';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100024901';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100920427';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100126642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100077494';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100091640';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105049';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047239';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100102322';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100086255';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100117344';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100011874';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100490784';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105055';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100920755';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100920791';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100489497';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047244';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047246';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100199835';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047260';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105073';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105087';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100535540';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101047300';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290408';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105089';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290409';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100199836';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105103';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100535541';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105125';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100290410';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA101105144';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:03' WHERE product_id = 'CMA100920854';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100290512';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100290513';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535542';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535543';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535544';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535545';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105161';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535546';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105172';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920856';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920873';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100290514';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920897';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105177';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105201';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920901';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105209';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105220';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100290515';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100290516';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920925';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100290517';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920937';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920944';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100920981';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105231';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100921654';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100921808';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100362353';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535547';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105244';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535548';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100086256';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535549';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077495';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126646';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100091641';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047379';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490785';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126783';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100921858';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047383';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105263';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100921896';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047388';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024903';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100290518';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489498';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047421';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047422';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293714';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100199837';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102323';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024904';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100117355';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100091642';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077496';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126784';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100011875';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100086257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100362354';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100011876';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489499';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490786';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047430';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047458';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047470';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100011877';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105268';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102517';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047472';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100091643';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024905';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105282';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105314';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105322';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535551';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105326';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105331';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047484';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047546';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489500';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105342';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293716';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100117367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077497';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535552';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535553';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024906';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100199838';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100117576';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490787';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047548';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490788';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100086258';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102520';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293717';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293718';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100011878';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293719';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105359';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489501';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100362355';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126787';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293720';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293721';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100091644';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105364';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126789';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105369';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100199839';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100117579';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100362356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077500';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489504';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100091645';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024908';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490793';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126790';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077502';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047572';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047575';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489505';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047582';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100199840';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100117581';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047597';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105387';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293722';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293723';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102521';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293724';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102522';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535554';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105424';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105427';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100094021';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105431';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105445';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024909';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047608';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047614';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100362357';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535555';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024910';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105447';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101105450';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535556';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100199841';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077503';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047679';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126791';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535557';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047682';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047719';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047726';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535558';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100535559';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100086259';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100011879';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047729';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100012077';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100489684';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047797';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047805';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100012078';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA101047814';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100086260';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100117583';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490794';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100121933';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100012079';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490795';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024911';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490797';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024912';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102523';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100012080';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100086261';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293726';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100024913';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100012081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100126792';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490802';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100293727';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100012082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100121941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100077504';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100490806';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100121945';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100362358';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:04' WHERE product_id = 'CMA100102526';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489685';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102527';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100490807';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094022';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100490808';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100490988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362360';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100535560';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102529';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199842';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100293728';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105451';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100024914';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100293730';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100086262';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362361';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100121949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077505';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489687';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126793';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100535561';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102530';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100535562';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100535563';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100121953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126795';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100121954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362362';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094024';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199843';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102532';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116490';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116540';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077506';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105455';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116552';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100024915';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105463';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116567';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100024916';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116636';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100122122';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126796';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077507';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012083';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094025';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116724';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489688';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116729';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105473';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116740';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012084';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094026';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116742';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102534';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116749';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362528';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116766';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116769';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116771';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116774';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100024917';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100024918';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100535564';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100545208';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077508';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116793';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116820';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489689';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126797';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102535';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199845';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094027';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199846';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105520';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105530';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491009';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362529';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491012';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116824';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116828';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116830';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116838';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100086263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077509';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105545';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116841';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199847';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094028';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491017';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102536';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012085';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126799';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105553';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126801';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077510';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100086264';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362533';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100024919';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116877';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116887';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101116972';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102537';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077511';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094029';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094030';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126802';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105566';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199850';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491020';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100086265';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100025173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362534';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012087';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012088';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100025174';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100122127';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105593';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105597';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105600';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012089';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105602';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105605';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100545209';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105611';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489690';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105627';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105660';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105662';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105668';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105672';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117047';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117063';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117067';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199851';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126803';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489691';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100025175';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117246';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105679';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105682';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117259';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102769';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117275';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117297';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117311';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117382';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491021';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117384';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117392';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094031';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105688';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105694';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105696';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105698';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100126805';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101105705';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100199852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100077512';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100086266';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100094032';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100489692';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117527';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100491023';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100545210';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117529';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102770';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100025176';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100102772';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100122129';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100362537';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100012090';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100545211';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100545212';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA101117573';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:05' WHERE product_id = 'CMA100545213';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126806';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489693';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100122133';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025177';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094034';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100102773';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025178';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545214';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199853';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077513';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545215';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545216';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105715';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077514';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545217';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545218';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489694';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126808';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491215';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199854';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025351';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105718';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126811';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117598';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117617';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105719';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117626';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126812';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105745';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105755';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489695';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126813';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105759';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491217';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025352';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100122144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077515';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117717';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491226';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491230';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100122153';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086268';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077516';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100362538';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489696';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126814';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100122156';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100123994';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100012091';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025353';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491232';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105786';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025354';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199855';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126815';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491233';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105811';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100012092';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094036';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077517';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100102774';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086269';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491234';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100126816';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100102775';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117827';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491380';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491381';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094037';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105849';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105900';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491388';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105905';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105911';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105914';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105917';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105926';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100127048';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105928';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100127051';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094038';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100123995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100362539';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025355';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545220';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117906';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117992';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199856';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199857';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086416';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100127055';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100102776';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100127056';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101117999';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100012093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077518';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086417';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100102778';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199858';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489697';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100127059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077519';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545221';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118068';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491389';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100362540';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094151';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118119';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118152';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105943';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100362541';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100127060';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100102779';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491539';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025358';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094152';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491540';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491541';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100012094';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077520';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118240';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199860';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491544';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105945';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545222';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105952';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086419';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100545223';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105954';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118256';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118308';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489699';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118322';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118325';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105961';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118357';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489700';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118365';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105974';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101118369';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100123997';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489701';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100491545';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100123998';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100123999';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100199861';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105977';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105985';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094153';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100124000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100077521';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100489702';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105990';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100025526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100362542';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100094154';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100086420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100362543';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101105994';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100102780';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA100127061';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101106008';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101106019';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:06' WHERE product_id = 'CMA101106022';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100127062';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106028';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106042';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545224';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106047';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106054';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100493882';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094155';
UPDATE products202503 SET brand_id = 'CEB002897', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100545225';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100086421';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100493883';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100199862';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100102781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100077522';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100489703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100362544';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012095';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100094156';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012096';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106059';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012097';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100494041';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100124001';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106066';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA101106077';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100012098';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:07' WHERE product_id = 'CMA100025527';
COMMIT;
