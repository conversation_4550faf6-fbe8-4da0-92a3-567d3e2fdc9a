BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101504996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101505992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506575';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101506988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101507838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101507981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508227';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101508989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:57' WHERE product_id = 'CMA101509619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101509994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510785';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510901';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101510980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101511990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101512956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101513981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514502';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514633';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514730';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101514981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101515036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:58' WHERE product_id = 'CMA101515054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515075';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515543';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101515984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516518';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101516985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517040';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101517999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101518995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101519991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520172';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:59' WHERE product_id = 'CMA101520695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101520997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521018';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521280';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521639';
COMMIT;
