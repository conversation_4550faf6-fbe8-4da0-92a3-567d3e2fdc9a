/**
 * 邮件发送API - Cloudflare Worker
 * 
 * 提供邮件发送功能，支持设置发件人、收件人、抄送人、主题和HTML内容
 */

// 环境变量: RESEND_API_KEY (在Cloudflare Workers设置中配置)

// 处理请求的主函数
export default {
  async fetch(request, env, ctx) {
    // 允许跨域请求
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        headers: corsHeaders
      });
    }

    // 只允许POST请求
    if (request.method !== 'POST') {
      return new Response(JSON.stringify({ error: '只支持POST请求' }), {
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }

    try {
      // 解析请求体
      const requestData = await request.json();
      
      // 验证API密钥
      const authHeader = request.headers.get('Authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(JSON.stringify({ error: '未提供有效的API密钥' }), {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }
      
      const apiKey = authHeader.replace('Bearer ', '');
      // 如果需要验证API密钥与环境变量中的密钥是否匹配，可以取消下面的注释
      // if (apiKey !== env.RESEND_API_KEY) {
      //   return new Response(JSON.stringify({ error: 'API密钥无效' }), {
      //     status: 401,
      //     headers: {
      //       'Content-Type': 'application/json',
      //       ...corsHeaders
      //     }
      //   });
      // }
      
      // 验证必要的字段
      if (!requestData.from || !requestData.to || !requestData.subject || !requestData.html) {
        return new Response(JSON.stringify({ error: '缺少必要的字段：from, to, subject, html' }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        });
      }
      
      // 准备发送邮件
      const emailData = {
        from: requestData.from,
        to: Array.isArray(requestData.to) ? requestData.to : [requestData.to],
        subject: requestData.subject,
        html: requestData.html
      };
      
      // 添加可选字段
      if (requestData.cc) {
        emailData.cc = Array.isArray(requestData.cc) ? requestData.cc : [requestData.cc];
      }
      
      if (requestData.bcc) {
        emailData.bcc = Array.isArray(requestData.bcc) ? requestData.bcc : [requestData.bcc];
      }
      
      if (requestData.reply_to) {
        emailData.reply_to = requestData.reply_to;
      }
      
      // 调用Resend API发送邮件
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(emailData)
      });
      
      // 获取响应
      const responseData = await response.json();
      
      // 返回结果
      return new Response(JSON.stringify(responseData), {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
      
    } catch (error) {
      // 处理错误
      return new Response(JSON.stringify({ error: `发送邮件时出错: ${error.message}` }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      });
    }
  }
}; 