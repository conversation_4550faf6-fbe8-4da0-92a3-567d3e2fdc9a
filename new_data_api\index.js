// 导入辅助模块
import { handleError } from './errorHandler.js';
import { corsHeaders } from './corsConfig.js';

// 定义路由处理函数
export default {
  async fetch(request, env, ctx) {
    try {
      // 处理预检请求
      if (request.method === "OPTIONS") {
        return new Response(null, { headers: corsHeaders });
      }

      const url = new URL(request.url);
      const path = url.pathname;
      
      // 路由匹配
      if (path.startsWith('/categories/tree')) {
        return await getCategoriesTree(env.DB);
      } else if (path.match(/^\/categories\/[^\/]+$/)) {
        const code = path.split('/').pop();
        return await getCategoryDetail(env.DB, code);
      } else if (path.match(/^\/products\/[^\/]+$/)) {
        const productId = path.split('/').pop();
        return await getProductDetail(env.DB, productId);
      } else if (path.startsWith('/products')) {
        const category = url.searchParams.get('category');
        const brand = url.searchParams.get('brand');
        const page = parseInt(url.searchParams.get('page') || '1');
        return await searchProducts(env.DB, category, brand, page);
      } else if (path.startsWith('/brands')) {
        const search = url.searchParams.get('search');
        return await getBrands(env.DB, search);
      }

      // 未找到匹配的路由
      return new Response(JSON.stringify({ error: 'Not Found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return handleError(error);
    }
  }
};

// 获取分类树结构
async function getCategoriesTree(db) {
  // 获取所有分类
  const { results: categories } = await db.prepare(`
    SELECT code, parent_code, level, name_cn, name_en, name_ru
    FROM categories
    ORDER BY level, code
  `).all();
  
  // 构建分类树
  const buildTree = (parentCode = '') => {
    return categories
      .filter(cat => cat.parent_code === parentCode)
      .map(cat => ({
        code: cat.code,
        name_en: cat.name_en,
        name_cn: cat.name_cn,
        name_ru: cat.name_ru,
        children: buildTree(cat.code)
      }));
  };
  
  const tree = buildTree();
  
  return new Response(JSON.stringify(tree), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取分类详情
async function getCategoryDetail(db, code) {
  const category = await db.prepare(`
    SELECT code, parent_code, level, name_cn, name_en, name_ru
    FROM categories
    WHERE code = ?
  `).bind(code).first();
  
  if (!category) {
    return new Response(JSON.stringify({ error: 'Category not found' }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
  
  return new Response(JSON.stringify(category), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取产品详情
async function getProductDetail(db, productId) {
  // 获取产品基本信息
  const product = await db.prepare(`
    SELECT product_id, model, brand_id, price_key, stock_key, 
           datasheet_url, image_list, parameters_key, description
    FROM products202503
    WHERE product_id = ?
  `).bind(productId).first();
  
  if (!product) {
    return new Response(JSON.stringify({ error: 'Product not found' }), {
      status: 404,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
  
  // 获取品牌信息
  const brand = await db.prepare(`
    SELECT id, name_cn, name_en, name_ru, logo_url, website
    FROM brands
    WHERE id = ?
  `).bind(product.brand_id).first();
  
  // 获取价格信息
  const { results: prices } = await db.prepare(`
    SELECT quantity, price
    FROM price
    WHERE code = ?
    ORDER BY quantity
  `).bind(product.price_key).all();
  
  // 获取库存信息
  const stock = await db.prepare(`
    SELECT stocks
    FROM stocks
    WHERE code = ?
  `).bind(product.stock_key).first();
  
  // 获取参数信息
  const { results: parameters } = await db.prepare(`
    SELECT languages, param_name, param_value
    FROM parameters
    WHERE code = ?
  `).bind(product.parameters_key).all();
  
  // 整理参数信息按语言分组
  const groupedParameters = {};
  parameters.forEach(param => {
    if (!groupedParameters[param.languages]) {
      groupedParameters[param.languages] = [];
    }
    groupedParameters[param.languages].push({
      param_name: param.param_name,
      param_value: param.param_value
    });
  });
  
  // 获取产品分类路径
  const categoryMap = await db.prepare(`
    SELECT category_code
    FROM product_category_map
    WHERE product_code = ?
  `).bind(productId).first();
  
  let categoryPath = [];
  if (categoryMap) {
    // 获取当前分类
    let currentCategoryCode = categoryMap.category_code;
    let categoryHierarchy = [];
    
    // 递归查找父分类构建完整路径
    while (currentCategoryCode) {
      const category = await db.prepare(`
        SELECT code, parent_code
        FROM categories
        WHERE code = ?
      `).bind(currentCategoryCode).first();
      
      if (!category) break;
      
      categoryHierarchy.unshift(category.code);
      currentCategoryCode = category.parent_code;
    }
    
    categoryPath = categoryHierarchy;
  }
  
  // 构建完整的产品详情响应
  const productDetail = {
    product_id: product.product_id,
    model: product.model,
    brand: brand ? {
      id: brand.id,
      name_en: brand.name_en,
      name_cn: brand.name_cn,
      name_ru: brand.name_ru,
      logo_url: brand.logo_url,
      website: brand.website
    } : null,
    prices: prices.map(p => ({
      quantity: p.quantity,
      price: p.price
    })),
    stock: stock ? stock.stocks : 0,
    parameters: groupedParameters,
    category_path: categoryPath,
    datasheet_url: product.datasheet_url,
    image_list: product.image_list ? JSON.parse(product.image_list) : [],
    description: product.description
  };
  
  return new Response(JSON.stringify(productDetail), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 产品搜索
async function searchProducts(db, category, brand, page) {
  const limit = 100; // 每页最多100条记录
  const offset = (page - 1) * limit;
  
  // 构建查询条件
  let conditions = [];
  let params = [];
  
  if (category) {
    conditions.push(`p.product_id IN (
      SELECT product_code 
      FROM product_category_map 
      WHERE category_code = ?
    )`);
    params.push(category);
  }
  
  if (brand) {
    conditions.push('p.brand_id = ?');
    params.push(brand);
  }
  
  const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
  
  // 获取总记录数
  const countQuery = `
    SELECT COUNT(*) as total
    FROM products202503 p
    ${whereClause}
  `;
  
  const totalCount = await db.prepare(countQuery).bind(...params).first();
  
  // 获取产品列表
  const productsQuery = `
    SELECT p.product_id, p.model, p.price_key, p.stock_key
    FROM products202503 p
    ${whereClause}
    LIMIT ? OFFSET ?
  `;
  
  const { results: productsList } = await db.prepare(productsQuery)
    .bind(...params, limit, offset)
    .all();
  
  // 获取每个产品的最低价格和库存信息
  const productsWithDetails = await Promise.all(productsList.map(async (product) => {
    // 获取最低价格
    const minPrice = await db.prepare(`
      SELECT MIN(price) as min_price
      FROM price
      WHERE code = ?
    `).bind(product.price_key).first();
    
    // 获取库存
    const stock = await db.prepare(`
      SELECT stocks
      FROM stocks
      WHERE code = ?
    `).bind(product.stock_key).first();
    
    return {
      product_id: product.product_id,
      model: product.model,
      min_price: minPrice ? minPrice.min_price : null,
      stock: stock ? stock.stocks : 0
    };
  }));
  
  // 构建响应
  const response = {
    total: totalCount ? totalCount.total : 0,
    page: page,
    results: productsWithDetails
  };
  
  return new Response(JSON.stringify(response), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

// 获取品牌列表
async function getBrands(db, search) {
  let query = `
    SELECT id, name_cn, name_en, name_ru, website, logo_url
    FROM brands
  `;
  
  let params = [];
  
  if (search) {
    query += ` WHERE name_en LIKE ? OR name_cn LIKE ? OR name_ru LIKE ?`;
    params = [`%${search}%`, `%${search}%`, `%${search}%`];
  }
  
  const { results: brands } = await db.prepare(query).bind(...params).all();
  
  return new Response(JSON.stringify(brands), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
} 