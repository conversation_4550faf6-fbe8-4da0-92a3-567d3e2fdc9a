# 存储服务配置指南

## KV 命名空间配置

### 创建 KV 命名空间

```bash
# 创建价格数据的 KV 命名空间
wrangler kv:namespace create PRICE_DATA

# 创建库存数据的 KV 命名空间
wrangler kv:namespace create STOCK_DATA
```

### KV 绑定配置

在 wrangler.toml 中添加以下配置：

```toml
# KV 命名空间绑定
kv_namespaces = [
  { binding = "PRICE_DATA", id = "your_price_namespace_id" },
  { binding = "STOCK_DATA", id = "your_stock_namespace_id" }
]
```

## R2 存储桶配置

### 创建 R2 存储桶

```bash
# 创建数据表文件的存储桶
wrangler r2 bucket create DATASHEETS

# 创建产品图片的存储桶
wrangler r2 bucket create PRODUCT_IMAGES
```

### R2 绑定配置

在 wrangler.toml 中添加以下配置：

```toml
# R2 存储桶绑定
r2_buckets = [
  { binding = "DATASHEETS", bucket_name = "datasheets" },
  { binding = "PRODUCT_IMAGES", bucket_name = "product-images" }
]
```

## Vectorize 索引配置

### 创建向量索引

```bash
# 创建产品向量索引
wrangler vectorize create products-index \
  --dimensions 1536 \
  --metric cosine
```

### Vectorize 绑定配置

在 wrangler.toml 中添加以下配置：

```toml
# Vectorize 索引绑定
vectorize_bindings = [
  { binding = "PRODUCTS_INDEX", index_name = "products-index" }
]
```

## D1 数据库配置

### 创建 D1 数据库

```bash
# 创建主数据库
wrangler d1 create little-field
```

### D1 绑定配置

在 wrangler.toml 中添加以下配置：

```toml
# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "little-field"
database_id = "your_database_id"
```

## 完整的 wrangler.toml 示例

```toml
name = "little-field"
main = "src/index.ts"
compatibility_date = "2023-01-01"

# KV 命名空间绑定
kv_namespaces = [
  { binding = "PRICE_DATA", id = "your_price_namespace_id" },
  { binding = "STOCK_DATA", id = "your_stock_namespace_id" }
]

# R2 存储桶绑定
r2_buckets = [
  { binding = "DATASHEETS", bucket_name = "datasheets" },
  { binding = "PRODUCT_IMAGES", bucket_name = "product-images" }
]

# Vectorize 索引绑定
vectorize_bindings = [
  { binding = "PRODUCTS_INDEX", index_name = "products-index" }
]

# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "little-field"
database_id = "your_database_id"
```

## 注意事项

1. 在实际部署时，需要将配置中的占位符（如 `your_price_namespace_id`、`your_database_id` 等）替换为实际的 ID
2. 确保在创建存储服务时使用正确的区域设置
3. 定期备份重要数据
4. 遵循最小权限原则配置访问权限