BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100862995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101465999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101466007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100863007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA101466020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:46' WHERE product_id = 'CMA100863009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863094';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101466995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863531';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863536';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA101467560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:47' WHERE product_id = 'CMA100863996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467604';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864108';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467785';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864371';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467932';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101467996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864589';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864733';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468309';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA101468517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:48' WHERE product_id = 'CMA100864968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100864979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100864996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468618';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865102';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468691';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468731';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468837';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865243';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101468902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865696';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469588';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469657';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865795';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865801';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA100865813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469820';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101469977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101470992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101471005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101471013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101471024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101471026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101471032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:49' WHERE product_id = 'CMA101471052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471533';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471574';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101471991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101472988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473423';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473913';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101473990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474482';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474498';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:50' WHERE product_id = 'CMA101474534';
COMMIT;
