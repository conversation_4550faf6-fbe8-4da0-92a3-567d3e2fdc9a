BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101521988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522093';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522706';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101522993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523188';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101523992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524690';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524769';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524855';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101524963';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525181';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525496';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525640';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525652';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525793';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101525997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526169';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:00' WHERE product_id = 'CMA101526392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526789';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526804';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101526981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527170';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527220';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527379';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527478';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527776';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101527995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528853';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101528994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529355';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529406';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529566';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529907';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101529998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530016';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530288';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530336';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530824';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101530992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101531002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101531039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101531056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101531067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101531084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101531097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:01' WHERE product_id = 'CMA101531107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531444';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531588';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531734';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531747';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101531983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532060';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532412';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532656';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101532966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533059';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533576';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533607';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533704';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533823';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533986';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101533996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534055';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534103';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534141';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534177';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534388';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534459';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:48:02' WHERE product_id = 'CMA101534509';
COMMIT;
