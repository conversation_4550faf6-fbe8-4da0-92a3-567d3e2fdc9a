BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729555';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729629';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085448';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101377981';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085453';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085498';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888692';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100082016';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378000';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353135';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996561';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353158';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353221';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888722';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888723';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378030';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378039';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100082017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729779';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888727';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888734';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996569';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497207';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996577';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353235';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100729956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730004';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089330';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497209';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996581';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353250';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888736';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888744';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888747';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888748';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996585';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996661';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888751';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888753';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888765';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085518';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353267';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730006';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996664';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085536';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353282';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353298';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353302';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353306';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730081';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353322';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996699';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996702';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378288';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353328';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353341';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353352';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730084';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100082018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730088';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353397';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497210';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996726';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730123';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101353436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730131';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085537';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089332';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996730';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100082019';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888769';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888777';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888780';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888782';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730133';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888785';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730136';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888787';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888790';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888794';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888797';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888800';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085552';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085559';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730138';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996762';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085577';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730140';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730142';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996777';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085637';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085752';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089333';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085753';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085755';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497212';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378417';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101354075';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378656';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101354160';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101354183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100082020';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996783';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996812';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888801';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888804';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888807';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888809';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888813';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888815';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888817';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730180';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101354248';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497213';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497214';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085769';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085775';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085783';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378677';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996821';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996829';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101354302';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888822';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100996834';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100089336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101085787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100082021';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101378702';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA101354444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100730209';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497215';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100497216';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888836';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888854';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:31' WHERE product_id = 'CMA100888857';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085801';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996889';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996893';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101378715';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101378734';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101354474';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085814';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085817';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497217';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730215';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082022';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101378800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730231';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996952';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730255';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730262';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497219';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085823';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100089337';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101354534';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101378882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730265';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996958';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996964';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085839';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996979';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888866';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888885';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888895';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888896';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100089338';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497220';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497221';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497222';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085840';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085848';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100996982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997003';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730280';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101378972';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730282';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100089339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730290';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888900';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101354621';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997024';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997029';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730307';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997035';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997048';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101378986';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379000';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888916';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888929';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082024';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730360';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997119';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085850';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997128';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085855';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085857';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085860';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085868';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100089340';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085874';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085880';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497225';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085884';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997132';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730364';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730411';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497226';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101354636';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379078';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100089341';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497227';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085904';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085910';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085920';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997147';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997153';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997158';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085923';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085927';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085929';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085935';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085938';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085948';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085953';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085958';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888937';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082026';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085973';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082027';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085978';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085983';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997197';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101085986';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997218';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086004';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101355030';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997222';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086007';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997234';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379128';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100089342';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100997242';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497228';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730451';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100089343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730454';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101355082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730478';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086013';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086041';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086045';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730536';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730547';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730549';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497229';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100888948';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730653';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086140';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100497428';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379171';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730667';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101355118';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379292';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730674';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086156';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086164';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086171';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101086181';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101355137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730705';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101355146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100082204';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA101379334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:32' WHERE product_id = 'CMA100730734';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730735';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355176';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355182';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730736';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497430';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100089344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730781';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086182';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100888954';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100888966';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100082205';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100089345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997324';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997335';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355194';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355204';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497431';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379485';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497432';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086194';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086200';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086216';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086225';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086227';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086241';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086247';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086259';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355224';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997354';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497433';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100082206';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100888967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730782';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100888975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730792';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100888984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730808';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730823';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086269';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997375';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997388';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997392';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355237';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086276';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997398';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730870';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997430';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997435';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730896';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100089346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730903';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086324';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355248';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730942';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997456';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730969';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730979';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100888996';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730984';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730991';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100730994';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497435';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100082207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731004';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997501';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379591';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731011';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731023';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997507';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997510';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889012';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889020';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731025';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355285';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731078';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355318';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355345';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889033';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889052';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889056';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889076';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889078';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889080';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889081';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086328';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086337';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086344';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086380';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086383';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086391';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100089347';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731094';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086394';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086416';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086422';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731104';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086426';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731165';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997519';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997522';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379734';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997532';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997537';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086458';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355381';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355394';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997565';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379803';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997570';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889082';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497436';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100082208';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100082209';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889108';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889115';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889127';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889128';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997573';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355400';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355409';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101379898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731223';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380085';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355415';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355436';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997581';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355452';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100089348';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355462';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355468';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086469';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731225';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889130';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889145';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889148';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889154';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100082210';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355472';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355493';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997595';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380281';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355503';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355524';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086598';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086614';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731237';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731288';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497438';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380308';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355550';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355560';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731301';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731305';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086653';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731308';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100089349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731315';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731322';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355601';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086661';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086677';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380336';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380350';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355611';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086679';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086686';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086700';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731344';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380363';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086707';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355663';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355696';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355704';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086737';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380390';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355725';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355762';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086757';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355774';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889157';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889163';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889166';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889170';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889174';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889178';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889184';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889189';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100889191';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100997605';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380456';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497439';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355792';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355810';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355815';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497440';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355819';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355823';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101380480';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355867';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086771';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355889';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086774';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086781';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355897';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355914';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355925';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101355930';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086788';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086790';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100731359';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100497441';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086814';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086847';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086864';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA100089350';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:33' WHERE product_id = 'CMA101086874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731367';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731453';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997639';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086887';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086893';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997642';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997666';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731456';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731457';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997677';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355948';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997682';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355955';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100082211';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997746';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355962';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355969';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086898';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355973';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086907';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355977';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086912';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355978';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997754';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355979';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997794';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086913';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086925';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101380646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731463';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101355995';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497442';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356021';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497443';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089351';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086927';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089352';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497444';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497445';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086939';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356023';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356031';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086958';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101380666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100082212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997804';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997843';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101380699';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997854';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100082213';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101086995';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087026';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087041';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087074';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997862';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356051';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997877';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087076';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356054';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997880';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087086';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356060';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356084';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356103';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356110';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356116';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087096';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356126';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101380792';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100082214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731497';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100082215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731580';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731581';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731594';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997894';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997923';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997928';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731604';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356140';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497446';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497447';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731631';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101380856';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731632';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731645';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889206';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087233';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731653';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731655';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731659';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731660';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731663';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731667';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101380906';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100997983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731668';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356175';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356190';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998017';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087256';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356200';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356211';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998028';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998031';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087262';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998034';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356257';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731746';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731749';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087304';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889210';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889213';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101381185';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356280';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356307';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497449';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089355';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889220';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089356';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731774';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731783';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356317';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087306';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889226';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998113';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731815';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731817';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731831';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356363';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998119';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998139';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889229';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731843';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100082216';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356427';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089538';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101381368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100082217';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087344';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356464';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731952';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889235';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998143';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356526';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889245';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497450';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087357';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889249';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087369';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497451';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087471';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356606';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100497452';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087478';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087509';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356621';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087514';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356631';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889250';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356646';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356661';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356663';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731953';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356668';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356705';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998147';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356710';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731979';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731992';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356739';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889253';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998182';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998187';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356749';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087527';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998191';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998197';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100731993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732010';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732023';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087568';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889254';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087575';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889262';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732028';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356777';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732039';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889286';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356791';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732043';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889289';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087581';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732067';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889290';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732082';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087622';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732088';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732092';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732096';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889292';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356855';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356918';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732107';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087636';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087680';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087687';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356922';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356944';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732121';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101356947';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087693';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732128';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101357019';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101357044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732157';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732185';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998199';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998214';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100089539';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101357057';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732190';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087716';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101087726';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732196';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101357075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100998303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732209';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101357078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100732216';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101357088';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA101357109';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:34' WHERE product_id = 'CMA100889304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100082218';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889307';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357118';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381384';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087731';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357130';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497453';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889311';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889314';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087742';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087752';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889317';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357186';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381411';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732229';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100732261';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357213';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087769';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497454';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087771';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357221';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357246';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381423';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087791';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087799';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357257';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357268';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889326';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357286';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357302';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381517';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357313';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100497455';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889333';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889343';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA100889346';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101381552';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101357323';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:35' WHERE product_id = 'CMA101087801';
COMMIT;
