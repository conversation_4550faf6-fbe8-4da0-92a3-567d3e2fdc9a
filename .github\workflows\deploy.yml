name: Deploy to Cloudflare Pages

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      deployments: write
    
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18.19.0
          cache: 'npm'
          
      - name: Install Dependencies
        run: |
          npm ci
          npm install wrangler --save-dev
        
      - name: Build
        run: npm run pages:build
        
      - name: Deploy to Cloudflare Pages
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        run: |
          npx wrangler pages deploy .vercel/output/static --project-name=${{ secrets.CLOUDFLARE_PROJECT_NAME }}
          # gitHubToken: ${{ secrets.GITHUB_TOKEN }} 