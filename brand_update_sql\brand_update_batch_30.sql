BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777848';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777863';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366411';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366428';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759199';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777906';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759229';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777934';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777937';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759260';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759297';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759301';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100777969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366780';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759312';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759318';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366787';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759332';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366836';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366864';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759408';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366965';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366978';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759424';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366986';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101366994';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA100759508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367033';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:34' WHERE product_id = 'CMA101367189';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367258';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778086';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778090';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778115';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367379';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778159';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759588';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367382';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367434';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367448';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367449';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367461';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367462';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367466';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367468';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778168';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759638';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778183';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759734';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367522';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367535';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367540';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778240';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367562';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367569';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778279';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778284';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778312';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367628';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778334';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367660';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778343';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367680';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100759973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367725';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367743';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778379';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778396';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367772';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778553';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778561';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367911';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367916';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760556';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367927';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367956';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778639';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760781';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778654';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367980';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100760869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367988';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367990';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367991';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761062';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367992';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778801';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761109';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778816';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101367997';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368000';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368003';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778871';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761142';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761150';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761156';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761173';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778892';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761188';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761194';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761200';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761202';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761209';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761212';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761215';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761216';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761217';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368130';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761251';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778906';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368154';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368162';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778933';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368173';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100778971';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761283';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761286';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761288';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761291';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779067';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368269';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779076';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368280';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368285';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368301';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761327';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761346';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779100';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368305';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368317';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368320';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779185';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100779215';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761387';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA101368344';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761437';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:35' WHERE product_id = 'CMA100761438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779247';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761440';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761471';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761474';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368353';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368418';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368500';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368528';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368535';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368551';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761510';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779329';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761514';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761525';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761529';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368559';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779398';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368562';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368586';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779433';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368588';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368608';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761563';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761596';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368614';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761604';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761605';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761609';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779446';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761612';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368637';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368640';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761620';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779532';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779534';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761660';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761665';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761671';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761674';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368644';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368657';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368666';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368668';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368694';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779634';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368697';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368699';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368703';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761899';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779645';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779700';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779756';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779771';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368737';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368759';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761971';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368769';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100761996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368771';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779796';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762005';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368781';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368790';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779808';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762036';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762039';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762052';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762061';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762065';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368796';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368813';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368827';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762210';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779858';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762225';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762242';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368866';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368876';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368905';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368906';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368910';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368932';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368952';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368961';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368966';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368981';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368982';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368983';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779978';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101368998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369003';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369009';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369036';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100779987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369039';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369044';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762362';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369050';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780037';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369092';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762396';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780044';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762434';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780059';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762450';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762478';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780139';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762490';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762500';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762501';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780149';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762510';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369122';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369123';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369181';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100762601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369191';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369251';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369260';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780273';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100763507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369276';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100763842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780305';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100763966';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100764117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780340';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100764183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA101369287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100780373';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:36' WHERE product_id = 'CMA100764215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780487';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780518';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369337';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780541';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369389';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764633';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764645';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764790';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780599';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764841';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780870';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369486';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369571';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369588';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369609';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100764974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369630';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369682';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369690';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100780971';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781013';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781023';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369724';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369799';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369816';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781089';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781277';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765417';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101369995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781283';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765428';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370005';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370024';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765434';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370058';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781378';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765565';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781415';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765610';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100765623';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370150';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA100781520';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:37' WHERE product_id = 'CMA101370195';
COMMIT;
