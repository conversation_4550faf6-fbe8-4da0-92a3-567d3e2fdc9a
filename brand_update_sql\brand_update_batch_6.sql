BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867281';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867282';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867292';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013514';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131687';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210136';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013515';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078423';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106438';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131688';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078424';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490678';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867323';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867328';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106439';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867332';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867334';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867337';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867340';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867349';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490679';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699231';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043257';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490680';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867354';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699246';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699260';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043258';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490683';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699293';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099438';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699298';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699335';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131691';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210137';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100013516';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100699354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078425';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100131696';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867384';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867402';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867409';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867416';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867418';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100867420';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106583';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573862';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100099439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100078426';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100210138';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100043259';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100106584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100573863';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100490684';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:27' WHERE product_id = 'CMA100087229';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573871';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210139';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867421';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867430';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043260';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699362';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013517';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699373';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078427';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699430';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043261';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078429';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573958';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490686';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210140';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210141';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100087230';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043262';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100099440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867438';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100106585';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573961';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078430';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699445';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699470';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699478';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100099441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699492';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043263';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013519';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043264';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867444';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490687';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699518';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078431';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867454';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490688';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699525';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867474';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100099442';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210142';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699531';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867488';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043265';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699571';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210143';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078432';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573965';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210144';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131860';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573967';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100087231';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100099443';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013520';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100106586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867498';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867557';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490689';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699578';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490690';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131861';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867563';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867578';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100106587';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078433';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867581';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867590';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100087232';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131862';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100099444';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699590';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210343';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043267';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013521';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078434';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100087233';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013522';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100106588';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013523';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699637';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131863';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013524';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699644';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013526';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867604';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013681';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699653';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867647';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867659';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867664';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867666';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867667';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867672';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867676';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699656';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131864';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131866';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078435';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100099445';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100087234';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490691';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043268';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867680';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210344';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100106589';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210345';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867699';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867705';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867707';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867710';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867720';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573975';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100106590';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100043269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699660';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573976';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100087235';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210346';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100490692';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699701';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100013683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100078436';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100099446';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100131867';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573977';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100210347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100573978';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867721';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100867729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:28' WHERE product_id = 'CMA100699753';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490693';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100573980';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100087236';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867735';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100087237';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100133501';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490694';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867752';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867774';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100043270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867777';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131869';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490695';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867780';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867796';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867798';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131870';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100573981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078437';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867803';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867808';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867809';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100013684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078438';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131871';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100573982';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490696';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131872';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490697';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867811';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867821';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867829';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867835';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867839';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867842';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210348';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100699766';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100087238';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078439';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100043271';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100133502';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867846';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867850';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867853';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867855';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490698';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100699793';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490699';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100573983';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490700';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100013685';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100699811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100699883';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210350';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100087239';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867862';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210351';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100013686';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100574130';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131877';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490702';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100133503';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100699935';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131879';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100043272';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100699982';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106593';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100013687';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100699990';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867870';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100087240';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100574132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078441';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131884';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490703';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106596';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100574133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700017';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100133504';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100043273';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078442';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131889';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106598';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100574134';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867874';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210353';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490704';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867884';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100043274';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490705';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100133505';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100013688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078443';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867886';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867890';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867893';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867896';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867904';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210354';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867912';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100087241';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106779';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100574135';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867933';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867949';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100574136';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100131890';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100490706';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100106780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700048';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100013689';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100043275';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100210355';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100867962';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100133506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100078577';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100013690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700073';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:29' WHERE product_id = 'CMA100700088';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100043276';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100574138';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100013691';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100087242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100131893';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867965';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867969';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867971';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100700098';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100210356';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100106782';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100133507';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100490707';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867977';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867987';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867991';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867992';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867994';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100133508';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100043277';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100106783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100867995';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100868020';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100868026';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100868041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100700107';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100013692';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100133509';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100106784';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100210357';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100490708';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100087243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100131894';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100043278';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100106790';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100210358';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100574139';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100490709';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100700115';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100490710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100700122';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100490711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100700133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100078578';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100131896';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100210359';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100131897';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100013693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100078579';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100868048';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100043279';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100043280';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100087244';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100574140';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100106792';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:30' WHERE product_id = 'CMA100133510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100078580';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100210360';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100574141';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868059';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868064';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868066';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100043281';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100131898';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868068';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868074';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100490712';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868075';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100490713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700135';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100490714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700151';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100043282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100078581';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868095';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100133511';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100210361';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100574143';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100031292';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100106940';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868107';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100087245';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700162';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100133512';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100087246';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100133513';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100031293';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100087247';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100131899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700182';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100490715';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100574145';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100043283';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100043434';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100106941';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100133514';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868113';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100210362';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100106942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100078582';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100031294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700345';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100133515';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100492984';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100131902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700347';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100043435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100078583';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100868121';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100574149';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100133516';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100210363';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100087248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700369';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100106943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700425';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100492985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700441';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100078584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100700460';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100131904';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:31' WHERE product_id = 'CMA100087249';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868129';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574150';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031295';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100078585';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043436';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031296';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700469';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492986';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106944';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210364';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100131905';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100087250';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700472';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031297';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210365';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100078586';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492987';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100133517';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043438';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868152';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868156';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868157';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868164';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868166';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868169';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868172';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868176';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868177';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868183';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868187';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700481';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043439';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492988';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106946';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574155';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100133518';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100078587';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100087251';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100131907';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868190';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700490';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100133519';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700492';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100133520';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100087252';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100131909';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100078589';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210367';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574157';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106947';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868192';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868198';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868207';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868211';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868212';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868215';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868218';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700499';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868224';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210368';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868225';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210369';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868227';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868229';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700522';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868231';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868233';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043441';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868235';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868241';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868245';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492990';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700531';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700621';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210370';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868249';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868253';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868255';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868258';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868260';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868264';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043442';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210371';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868271';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100133521';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868275';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868283';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106949';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868290';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868297';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868301';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868302';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868330';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868331';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868333';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574160';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700625';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868344';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574161';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100132158';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106950';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100133522';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492992';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043443';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700726';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210372';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100078590';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100031302';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868349';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868381';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700770';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700806';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100868391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700811';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100087253';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043444';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106951';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492993';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106952';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210633';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492994';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100078592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700857';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574165';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210634';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106953';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574166';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100132159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700905';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100492995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100078594';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100106954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100700908';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574167';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100043445';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574169';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100132160';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574171';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100133523';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100574173';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:32' WHERE product_id = 'CMA100210635';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868395';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868399';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100106955';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868402';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100574176';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868405';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868408';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043446';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043447';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100106958';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100574178';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868412';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868423';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868425';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868428';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868430';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868442';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100492996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078595';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868444';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868446';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868448';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868452';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868456';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132161';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868459';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100031303';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100087254';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868464';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868468';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868473';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868475';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868477';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100210636';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868478';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868487';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868497';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100492997';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078597';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107122';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100210637';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100087255';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868501';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868541';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868545';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107123';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132168';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100210638';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043448';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107124';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868547';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868556';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132169';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868560';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107125';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868567';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100210639';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043449';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100700920';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100574180';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100087256';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100700988';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205360';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205369';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868583';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701044';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701062';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701064';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100492998';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701136';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868598';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868610';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043450';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868611';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100492999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575932';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575933';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100031304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078599';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107127';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132175';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100210640';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701148';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868632';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205400';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868642';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107130';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575935';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107131';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107132';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107133';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100493000';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205409';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205415';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868675';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132325';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100493001';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575939';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107134';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701164';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575940';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078600';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078601';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100493002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078602';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701182';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043451';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868713';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701190';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701228';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100210641';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701255';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100210642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701257';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575982';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100087257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701258';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100031305';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100493003';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100031306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701272';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701305';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701314';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100217097';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205533';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205536';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205544';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205552';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100087258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701331';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701336';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701348';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868751';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868770';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575987';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868783';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078603';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205579';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043452';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701353';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107141';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107143';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100031307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701382';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701404';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107145';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100217098';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100701425';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205610';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868788';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575990';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100493004';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043453';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100043454';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100107147';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100132330';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205623';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205665';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA101205680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100078604';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100031308';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868801';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868821';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868823';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575991';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868829';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868841';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100868844';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:33' WHERE product_id = 'CMA100575992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701444';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100575996';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043455';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493005';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078605';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132333';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078606';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087260';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701477';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101205726';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868852';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868867';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043456';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701522';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132338';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100575998';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078782';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101205738';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087261';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107402';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107403';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868881';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868886';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868889';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868893';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868895';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101205786';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107404';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043457';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100031309';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078783';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101205790';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217101';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107405';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100575999';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100576000';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868925';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101205810';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868956';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701526';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100576001';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101205940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078784';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100868972';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869008';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100031310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132339';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869009';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087262';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100576002';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100576003';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869029';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107406';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869038';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100031311';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100576004';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043458';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493007';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043459';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493008';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043460';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043491';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217102';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493009';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100576005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701535';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493010';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101205989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078785';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087263';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701621';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869042';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217104';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100578273';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132340';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869053';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869057';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869060';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217105';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107407';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132343';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087264';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132344';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100107408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701641';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100031312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701646';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701650';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101206000';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100031313';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869099';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043492';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869111';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100701682';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132347';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869119';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100043493';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132348';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100869141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100132349';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100217106';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA101206150';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100578275';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100087265';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100493011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:34' WHERE product_id = 'CMA100078787';
COMMIT;
