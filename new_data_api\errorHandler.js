import { corsHeaders } from './corsConfig.js';

export function handleError(error) {
  console.error('API Error:', error);
  
  // 根据错误类型返回不同的状态码
  let status = 500;
  let message = 'Internal Server Error';
  
  if (error.message.includes('not found') || error.message.includes('No such')) {
    status = 404;
    message = 'Resource not found';
  } else if (error.message.includes('timeout') || error.message.includes('connection')) {
    status = 503;
    message = 'Database connection error';
  } else if (error.message.includes('required parameter') || error.message.includes('invalid')) {
    status = 400;
    message = error.message;
  }
  
  return new Response(JSON.stringify({ 
    error: message,
    details: error.message 
  }), {
    status: status,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
} 