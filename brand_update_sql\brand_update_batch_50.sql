BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489735';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101489999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490106';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490119';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490221';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:53' WHERE product_id = 'CMA101490239';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490249';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490377';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490441';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490591';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490669';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490723';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490749';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490838';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490953';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490977';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490990';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101490999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491039';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491044';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491127';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491135';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491179';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491363';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491369';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491515';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491725';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101491996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492041';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492095';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492184';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492324';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492345';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492550';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492661';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492757';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492878';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492915';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101492991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493013';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493028';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493102';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493173';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493226';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493229';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493293';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493410';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493460';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493551';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493586';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493600';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493629';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493663';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493681';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493791';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493840';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493846';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493900';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101493989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494020';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494151';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494268';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494382';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494424';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494481';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494676';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494713';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494753';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494760';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494792';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494850';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494880';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494964';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494973';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101494991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495011';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495137';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495187';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495262';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495316';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495334';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495413';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495452';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495493';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495505';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495594';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495650';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495696';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495716';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495755';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495775';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495833';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495893';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495909';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495949';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495960';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101495993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496022';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496152';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496163';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496199';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496241';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:54' WHERE product_id = 'CMA101496311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496349';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496399';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496442';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496564';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496632';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496689';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496745';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496898';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496942';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101496974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497001';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497085';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497131';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497154';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497218';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497256';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497269';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497284';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497314';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497342';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497427';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497456';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497491';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497687';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497787';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497834';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497871';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497874';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497926';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497967';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101497984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498017';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498038';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498046';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498063';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498072';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498090';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498104';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498110';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498252';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498311';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498323';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498338';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498403';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498431';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498440';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498457';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498585';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498641';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498648';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498665';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498692';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498702';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498739';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498780';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498802';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498831';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498854';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498861';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498873';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498887';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498891';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498892';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498905';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498937';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498951';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498957';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101498991';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499000';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499035';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499057';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499078';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499088';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499126';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499149';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499160';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499178';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499208';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499253';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499335';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499374';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499404';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499414';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499432';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499451';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499469';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499489';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499511';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499537';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499572';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499583';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499621';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499626';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499670';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499698';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499705';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499748';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499770';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499830';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499910';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499947';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499975';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101499988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500081';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500092';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500118';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500133';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500201';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500272';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500298';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500321';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500340';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500354';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500358';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500447';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500553';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500642';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500655';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500721';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500728';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500750';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500768';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500809';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500836';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500837';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500870';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500904';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500982';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101500993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501034';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501066';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501128';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501436';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501477';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501627';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501643';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501797';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101501825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502289';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502294';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:55' WHERE product_id = 'CMA101502357';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502365';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502391';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502421';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502437';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502450';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502479';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502507';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502516';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502524';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502558';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502613';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502710';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502771';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502801';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502839';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502896';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502968';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101502996';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503026';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503056';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503069';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503084';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503099';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503140';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503145';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503224';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503232';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503278';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503300';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503319';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503332';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503455';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503488';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503542';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503589';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503599';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503610';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503649';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503685';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503709';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503842';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:56' WHERE product_id = 'CMA101503860';
COMMIT;
