BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001369', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100268525';
UPDATE products202503 SET brand_id = 'CEB000194', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100032552';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103721';
UPDATE products202503 SET brand_id = 'CEB002282', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100415354';
UPDATE products202503 SET brand_id = 'CEB001950', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101108555';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103579';
UPDATE products202503 SET brand_id = 'CEB000317', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100064816';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103586';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103590';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100252938';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100114156';
UPDATE products202503 SET brand_id = 'CEB000317', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100064817';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103593';
UPDATE products202503 SET brand_id = 'CEB000317', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100064818';
UPDATE products202503 SET brand_id = 'CEB001400', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101169312';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100086022';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103596';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100018419';
UPDATE products202503 SET brand_id = 'CEB001400', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101169323';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103598';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103722';
UPDATE products202503 SET brand_id = 'CEB001353', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101169318';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103601';
UPDATE products202503 SET brand_id = 'CEB000317', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100064819';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100605964';
UPDATE products202503 SET brand_id = 'CEB001950', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101108570';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103603';
UPDATE products202503 SET brand_id = 'CEB002282', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100415355';
UPDATE products202503 SET brand_id = 'CEB002282', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100888147';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010546';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100605965';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103610';
UPDATE products202503 SET brand_id = 'CEB000317', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100064820';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010547';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103723';
UPDATE products202503 SET brand_id = 'CEB000211', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100039161';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038798';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103626';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103724';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100006876';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100086023';
UPDATE products202503 SET brand_id = 'CEB000317', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100984721';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038799';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100018420';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010549';
UPDATE products202503 SET brand_id = 'CEB000211', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100039162';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100121590';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100114157';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100484640';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103725';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100489274';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100011613';
UPDATE products202503 SET brand_id = 'CEB000317', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100984743';
UPDATE products202503 SET brand_id = 'CEB000211', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170410';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100252939';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100266673';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103726';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103632';
UPDATE products202503 SET brand_id = 'CEB000211', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170418';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100121592';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038800';
UPDATE products202503 SET brand_id = 'CEB000233', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045614';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103727';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010551';
UPDATE products202503 SET brand_id = 'CEB000211', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170421';
UPDATE products202503 SET brand_id = 'CEB000233', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045615';
UPDATE products202503 SET brand_id = 'CEB000233', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045616';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100605966';
UPDATE products202503 SET brand_id = 'CEB000233', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045617';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038801';
UPDATE products202503 SET brand_id = 'CEB000233', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045618';
UPDATE products202503 SET brand_id = 'CEB000233', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045619';
UPDATE products202503 SET brand_id = 'CEB000233', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170449';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100112635';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100121593';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100112638';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100280222';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100121594';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100112640';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045929';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038802';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100121595';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103648';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038803';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045930';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045931';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100112641';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038804';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038805';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045932';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045933';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045934';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010552';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103728';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045935';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010553';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103729';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045936';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103730';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103660';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045937';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045989';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010554';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103662';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688349';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100112643';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038806';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038807';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688354';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038808';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688358';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688363';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103698';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100112645';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045990';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038809';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038810';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038811';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100112824';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010555';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010556';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113011';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688383';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688404';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010557';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688413';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038812';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688421';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688423';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688425';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688449';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010558';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010559';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010560';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010561';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045991';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010562';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045992';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010563';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688452';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045993';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010564';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045994';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688454';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100045995';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038813';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688457';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038814';
UPDATE products202503 SET brand_id = 'CEB000476', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100688510';
UPDATE products202503 SET brand_id = 'CEB000211', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170423';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100086024';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113158';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103705';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113163';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113176';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101103787';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113179';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100489275';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113182';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038815';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113183';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038816';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170455';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100252940';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166562';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166563';
UPDATE products202503 SET brand_id = 'CEB000637', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100343274';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166564';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010565';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100114158';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100103197';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170464';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100011614';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100006877';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100463884';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100463885';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170476';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100463886';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170485';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038817';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170502';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166565';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038818';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100280223';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170531';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166566';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010701';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010702';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010703';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010704';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100018421';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038819';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038820';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038821';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100605967';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100038822';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100121597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100137814';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100605968';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113186';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100266674';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113401';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166567';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100113405';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170533';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170537';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170549';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170554';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170557';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100605969';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170560';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010705';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170568';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010706';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170580';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010707';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170583';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010708';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170589';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170594';
UPDATE products202503 SET brand_id = 'CEB000235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA101170612';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166568';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166569';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100166570';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100010709';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100006878';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100484642';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:54' WHERE product_id = 'CMA100605970';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038823';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006879';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100114159';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170716';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018422';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100280224';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103794';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010710';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100113410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100138011';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100489276';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100266871';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100121598';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006880';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100086025';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100091413';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100011615';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463887';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038824';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170728';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038825';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100266872';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170739';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038826';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170750';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463888';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038827';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038994';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100086026';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170781';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463889';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038995';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170785';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038996';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038997';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252941';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100484647';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100489277';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018423';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100267155';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038998';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018424';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100038999';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100039000';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100011616';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010711';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100039001';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010812';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100039002';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010813';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170401';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010814';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103817';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170404';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100280225';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170407';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463890';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100280226';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170801';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010815';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006881';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170808';
UPDATE products202503 SET brand_id = 'CEB000798', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170824';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100267156';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006882';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100113545';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605971';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100113547';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100091414';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006883';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006884';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010817';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006885';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010818';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010819';
UPDATE products202503 SET brand_id = 'CEB000122', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101225266';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010820';
UPDATE products202503 SET brand_id = 'CEB000122', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101225270';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605972';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010821';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170409';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010822';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170416';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010823';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170426';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010824';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170428';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170433';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605973';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006886';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170448';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010825';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170474';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006887';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010826';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170479';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170482';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006888';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605974';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170487';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170489';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170490';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170491';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170493';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170499';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170505';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170508';
UPDATE products202503 SET brand_id = 'CEB000206', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101170512';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100011617';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100489278';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605975';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100484655';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100113552';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100372915';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018425';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006889';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006890';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100121600';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100372916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100138023';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100086027';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100044123';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006891';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100091415';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100280227';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006892';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463891';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100280228';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103825';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010827';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100114160';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018426';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006893';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100268956';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103828';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103846';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252943';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252944';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252945';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100372917';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100114161';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605976';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100121601';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100216192';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018427';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103847';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100268957';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100484657';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100268958';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100113558';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100268959';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103853';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018428';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103858';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100011618';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100114162';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100121603';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252946';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010828';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252947';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010829';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010830';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100280229';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100086028';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010831';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463892';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100044124';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100091416';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010832';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100114163';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103863';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010833';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100489279';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010834';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010835';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010869';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010871';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010872';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463893';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463894';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103866';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103893';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100114164';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252948';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463895';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463896';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100484660';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018429';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010873';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100230360';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100489280';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100268960';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100121768';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100280230';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101045384';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103897';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100114165';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA101103904';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100044125';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100006894';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100011619';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100086029';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100018430';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100268961';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100091417';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252949';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463897';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100605978';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010876';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100010877';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463898';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252950';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463899';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100252951';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463900';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463901';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463902';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463903';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463904';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463905';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463906';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100463907';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100489281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:55' WHERE product_id = 'CMA100230378';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100463908';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103905';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605979';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100086030';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100268962';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121771';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100280231';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045399';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044126';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018431';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006895';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114166';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018432';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045415';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100091418';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010878';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100280232';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605980';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605981';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121772';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121773';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100252952';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103916';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006896';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100463909';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044127';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100463910';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100463911';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018433';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484661';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010879';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010880';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010881';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100463912';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100463913';
UPDATE products202503 SET brand_id = 'CEB002502', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100463914';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100280234';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100280235';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100280236';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484670';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605982';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045420';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103925';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103934';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045448';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018434';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045462';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114167';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103939';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103949';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103957';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010882';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045468';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045479';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100091419';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045487';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045497';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045501';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114168';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100489282';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121776';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605983';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006897';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044128';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234634';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100280237';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100086031';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484672';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010883';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010884';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103960';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103974';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234064';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103983';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100011620';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484673';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018435';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234640';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045503';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484675';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044129';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234642';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605984';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100086032';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234655';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006898';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234657';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006899';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006900';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103986';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605985';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100010885';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103991';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170088';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170181';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170200';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170231';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170234';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100252953';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170247';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100252954';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234077';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045508';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045513';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484677';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006901';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234766';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234102';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114169';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018436';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234110';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018437';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100091420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484678';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044130';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045521';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045527';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484684';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114170';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100489283';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170314';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170331';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484685';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170336';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170436';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234129';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170492';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044131';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045528';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234135';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234152';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234165';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234181';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234785';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234791';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234799';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484687';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234189';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234807';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170497';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045541';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234222';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100252955';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100113560';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170542';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045551';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170575';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170599';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170607';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170675';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234229';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170691';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045570';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170705';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101103994';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100091421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100360824';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100011621';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605986';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100283518';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018438';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100086033';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100489284';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234827';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234937';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234243';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234253';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045583';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234941';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045594';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104021';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044132';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045602';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045609';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170717';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170724';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234259';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170771';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170779';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234260';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234287';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100086034';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234290';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234942';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234293';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234947';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234950';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170804';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170809';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006902';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170822';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100252956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121777';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484690';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006903';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100006904';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170828';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170834';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100256064';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170866';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170875';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100256065';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100256066';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234307';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234334';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007089';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234341';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170880';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234344';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114171';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114172';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104052';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170892';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121778';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007090';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484693';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121779';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484699';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100360825';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044133';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100091422';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018440';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170896';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170901';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170975';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234347';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121780';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484872';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121781';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234958';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007091';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100086035';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045613';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104068';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104075';
UPDATE products202503 SET brand_id = 'CEB000648', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101170985';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104080';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114173';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007092';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234962';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100113562';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234969';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018441';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234371';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234373';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018442';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234382';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100283519';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234386';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007093';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100283520';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007094';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100489285';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234974';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234988';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104082';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605987';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104153';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018443';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104158';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104189';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234994';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605988';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100011622';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605989';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100283521';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100605990';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114174';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100484873';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007095';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007096';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007097';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104199';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007098';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235006';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235016';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100011623';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104205';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235023';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018444';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018445';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104286';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018446';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018447';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104289';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235057';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235067';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235076';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235089';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104294';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045647';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114175';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018448';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100489286';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234390';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114176';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114177';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121784';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114178';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239342';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114184';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100091423';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239356';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007099';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007100';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100121785';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239363';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044134';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239369';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101104300';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239372';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235097';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234450';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235113';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101235114';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101234454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100360826';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239375';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100114185';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044135';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100044136';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239380';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018684';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018685';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100113712';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100113715';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100018687';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100283523';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100007101';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045663';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045674';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045687';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045689';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA100256067';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045693';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045698';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101045700';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:56' WHERE product_id = 'CMA101239384';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101045710';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100114285';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100113717';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283524';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100113718';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100091424';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007102';
UPDATE products202503 SET brand_id = 'CEB003274', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239389';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100044137';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104308';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239378';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104360';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256068';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256069';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100489287';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235117';
UPDATE products202503 SET brand_id = 'CEB001376', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101234458';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117635';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104366';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104378';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256070';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100484874';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104384';
UPDATE products202503 SET brand_id = 'CEB001358', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101045717';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239383';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239387';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239388';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100044138';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283525';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007103';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100360829';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239393';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100086036';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100484875';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235145';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235152';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605991';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605992';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283526';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100091425';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100011624';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007104';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117636';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605993';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605994';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256071';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235164';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007105';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235176';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235187';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018688';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235191';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018689';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018690';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117637';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018691';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244105';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018692';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007106';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283527';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018693';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244117';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244124';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007107';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007108';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007109';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244133';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007110';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244174';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018694';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007111';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244178';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007112';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018695';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007113';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007114';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101104423';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235198';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007115';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100121786';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100044139';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239398';
UPDATE products202503 SET brand_id = 'CEB000226', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100044140';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100091426';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239406';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244182';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239407';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239408';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283528';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239409';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239411';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100011625';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244189';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007116';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018696';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007216';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007217';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100113721';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007218';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007219';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007220';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100360832';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283529';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244214';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007222';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007223';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007224';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117638';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007225';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007226';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244217';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007227';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007228';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244220';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117639';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007229';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244221';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117640';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018697';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007230';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007231';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007232';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244225';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007233';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244228';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007234';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244230';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283530';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007235';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244232';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007236';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244234';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007237';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018698';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244237';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117641';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117642';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235203';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117643';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117644';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283531';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007238';
UPDATE products202503 SET brand_id = 'CEB001503', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101235212';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007239';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018699';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018700';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605995';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007240';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007241';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244240';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244246';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244258';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244263';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018701';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244266';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244269';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244274';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117645';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244282';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117646';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117647';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244285';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256072';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244290';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244294';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244299';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244304';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605996';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244305';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605997';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018702';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605998';
UPDATE products202503 SET brand_id = 'CEB001664', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100018703';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244312';
UPDATE products202503 SET brand_id = 'CEB002368', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101244317';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117648';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100605999';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256073';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606000';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239413';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606001';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239417';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239419';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239425';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100489288';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606002';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100086037';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256074';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256075';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100256076';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606003';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283532';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239440';
UPDATE products202503 SET brand_id = 'CEB002141', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA101239449';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259312';
UPDATE products202503 SET brand_id = 'CEB001310', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100259313';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117649';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606004';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117650';
UPDATE products202503 SET brand_id = 'CEB003235', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100606005';
UPDATE products202503 SET brand_id = 'CEB001468', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100283623';
UPDATE products202503 SET brand_id = 'CEB000052', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100007242';
UPDATE products202503 SET brand_id = 'CEB001025', updated_at = '2025-07-16 20:43:57' WHERE product_id = 'CMA100117651';
COMMIT;
