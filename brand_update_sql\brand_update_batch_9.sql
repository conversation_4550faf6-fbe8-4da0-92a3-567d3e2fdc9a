BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706211';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873702';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706222';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578973';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133210';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873724';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873733';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217595';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873738';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109443';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706232';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100079168';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706243';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706252';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100087812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706267';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706273';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044092';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873781';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044255';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873793';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100079169';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873798';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133372';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493597';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109478';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578974';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217596';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873800';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706289';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706290';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032263';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493598';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873810';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100079171';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706294';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493599';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109479';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133379';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133380';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706302';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706312';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873823';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133383';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493600';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873844';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100079172';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109480';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578976';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109481';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032264';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133385';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044258';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873855';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873867';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032265';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100087813';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109483';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217598';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109486';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100873869';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133388';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109487';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100873893';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493602';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217599';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087814';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109488';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109489';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087815';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044259';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706313';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706358';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706369';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578979';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044260';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079174';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217600';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100873921';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217601';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133389';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493603';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100873978';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100873995';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874002';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032266';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874005';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087816';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874014';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044261';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032267';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706402';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032268';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874016';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874076';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874080';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874083';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706421';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706425';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874124';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874127';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578981';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874128';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133392';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706441';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874137';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044263';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109490';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044264';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079175';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133394';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578993';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217602';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032270';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578994';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133395';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706459';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087817';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578995';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706466';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874151';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706468';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079176';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032272';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706501';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706506';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874165';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874188';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493605';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079177';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109491';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100578998';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874194';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706516';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706524';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044265';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044266';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874197';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044267';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032273';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706566';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706570';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706576';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706596';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493606';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217605';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044269';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079325';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100579000';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087819';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874210';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706598';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493607';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087820';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133526';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109496';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044271';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217606';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100579004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079326';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044272';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217607';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493608';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044273';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100579005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706618';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874293';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706620';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032274';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706675';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109497';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079327';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044274';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133530';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109498';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044275';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133532';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133533';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100579006';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706680';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706688';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706715';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706720';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133534';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100087821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706724';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100109500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706735';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100493610';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100706737';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100133536';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032276';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100079328';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100217608';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100579008';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874334';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874346';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100032277';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100874353';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:48' WHERE product_id = 'CMA100044276';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874368';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874376';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133540';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032278';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044277';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044278';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100079329';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706746';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580465';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100217609';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133541';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133542';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100217610';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493611';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874394';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874402';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874429';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100217611';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100217612';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109650';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706755';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493612';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100079330';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133544';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100217613';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087823';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044280';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109651';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706759';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874476';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874541';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032279';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032280';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044281';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706784';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874574';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044282';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706797';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706811';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706818';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493613';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100220281';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580468';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133546';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032281';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044452';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032282';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874575';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706829';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874589';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874592';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874594';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706849';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109655';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706856';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706869';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032486';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087824';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874619';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044453';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874628';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874636';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874639';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100079331';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087826';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133547';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580469';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044454';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100220282';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493615';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109656';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493616';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109657';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580471';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100220283';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032487';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109658';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109660';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874768';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032488';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874770';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874781';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706953';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706956';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044455';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100079332';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133721';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109661';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874783';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874794';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874802';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874807';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032489';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100220284';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109665';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109666';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100220285';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087827';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109668';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087828';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580472';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706968';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032490';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100706978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707019';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707024';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044456';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032491';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580473';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100032492';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087829';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707066';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044457';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707079';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133722';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087831';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707091';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109673';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707107';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100087832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707116';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707117';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100493736';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100109675';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100133723';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707140';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100044458';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707148';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100580474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100079333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100707159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874867';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:49' WHERE product_id = 'CMA100874877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079334';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087833';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493737';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109678';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079335';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109823';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133724';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032493';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109824';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580536';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707164';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707175';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580537';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087834';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032494';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133727';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707194';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707207';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707208';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109825';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079336';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100874910';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100874918';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109826';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707210';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079337';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087835';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109828';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707226';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707230';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032495';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044463';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707231';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220289';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044464';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493739';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032496';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707262';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220290';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133730';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109829';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707278';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707283';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707293';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044466';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079338';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493740';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087836';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580539';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109830';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100874920';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875089';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875098';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875099';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032497';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707294';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109831';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087837';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109832';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493741';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580540';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707329';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493743';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109833';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220291';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044467';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875127';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875132';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875135';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087838';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109834';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133733';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109836';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707339';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580545';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493744';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707346';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079340';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707359';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044468';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220293';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109837';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109838';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875140';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109839';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707399';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875144';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875150';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493745';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109841';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087839';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079341';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044469';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220294';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707409';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032499';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044470';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079342';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707426';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875168';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875195';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044471';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087840';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100032500';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087841';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133734';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220295';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109842';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133735';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079343';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109843';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220296';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875200';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875219';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875222';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100087842';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875230';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100220297';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100079344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100580550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707447';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100707450';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100044473';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875233';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100875235';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100493746';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100109845';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:50' WHERE product_id = 'CMA100133738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707451';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044474';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707477';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707493';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220299';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707497';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707525';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707528';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707543';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580692';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707547';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707550';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875261';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044476';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875263';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580694';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493747';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032501';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220304';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109846';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707555';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100087843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079345';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875308';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875313';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109847';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044478';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032502';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133741';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580695';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707607';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100087844';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707614';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493748';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875323';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079346';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032504';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875372';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493749';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580696';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109849';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707616';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220305';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032505';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044479';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032506';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133742';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707622';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707639';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109850';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133744';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220307';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493750';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580699';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707649';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032507';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493751';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580701';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707676';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079348';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707694';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707724';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875383';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875434';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100087845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079350';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580702';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580703';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032508';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707733';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580705';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875435';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707739';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707744';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875446';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707746';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875448';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875460';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580707';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493753';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109851';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109852';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220308';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079351';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707762';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044481';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109857';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707801';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580708';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133898';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707810';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109864';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109865';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133899';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875474';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707856';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707864';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707876';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580711';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875501';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707880';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707891';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100493754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100133901';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100032509';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100087846';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100220310';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109869';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044482';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875532';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707964';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875542';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100875556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707970';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100079353';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100044483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100707988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100708010';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100109870';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:51' WHERE product_id = 'CMA100580712';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100580714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100079489';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044484';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220311';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100087847';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493755';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109873';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708017';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493756';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044485';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100133902';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708036';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875558';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100032510';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875593';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875597';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100133904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708039';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220312';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100087848';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100079491';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493757';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100580715';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875598';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875643';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875648';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044486';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100580717';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100580718';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708086';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875649';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708097';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708102';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708109';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708123';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708124';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708137';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100032511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708139';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708142';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875772';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708145';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708173';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708175';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875797';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875799';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109987';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044487';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100133905';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708185';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875820';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100580851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100079492';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875821';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100087849';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875824';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875826';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493758';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100133906';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100133911';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100079493';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708205';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109989';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100087850';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100580854';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044488';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220315';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875837';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220316';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708217';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875848';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708222';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109990';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708223';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708226';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100087851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708229';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100032512';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493760';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100032513';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708233';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875879';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493761';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875884';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875885';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044489';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875887';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100032514';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100079495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875905';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708244';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109991';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100032515';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708245';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044490';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100133912';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220317';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875936';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875942';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875947';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875958';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875960';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875967';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100109992';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100079496';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100493762';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100087852';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100708256';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100133919';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100580855';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100032672';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875970';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875974';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875975';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875976';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100875977';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100044491';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:52' WHERE product_id = 'CMA100220319';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100493946';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100875979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100079497';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100133926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708262';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100044492';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100493947';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100032673';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580862';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100133929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708266';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100079498';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100044493';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100875993';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100087853';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100109994';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100109995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708279';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100133930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100079499';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100044494';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100109996';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580864';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100876002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708287';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100876008';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100876017';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580865';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100876026';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100493948';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100079500';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100133931';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580866';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100220320';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100087854';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100220321';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100493949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708289';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100109997';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100044495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100876056';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100032674';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100087855';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580868';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100044496';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580869';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100079501';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100580870';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100876087';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100032675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:53' WHERE product_id = 'CMA100708311';
COMMIT;
