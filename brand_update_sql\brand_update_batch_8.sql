BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704071';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132914';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100578623';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871595';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100043880';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100493393';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100031834';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217304';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100079121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704092';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100031835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704095';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871601';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208752';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704113';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871612';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871621';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871625';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087491';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871635';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871640';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704149';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132915';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108661';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108662';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704192';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704195';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208768';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108664';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217305';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871658';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100079122';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871661';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087492';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132917';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871693';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100043881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100079124';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100043882';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217306';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA101208794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704210';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100087493';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100217307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704223';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871704';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704233';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704246';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871708';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704254';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100108665';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704332';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871725';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871761';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100704339';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100871779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:40' WHERE product_id = 'CMA100132921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079125';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132924';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100087494';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217308';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100108666';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031836';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578625';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043883';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704343';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100493394';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA101208818';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871780';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100108667';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578627';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100108668';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578628';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100087495';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132926';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100493395';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100087617';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079126';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031837';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217309';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100493396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704370';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704375';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704376';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043884';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704377';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704385';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217310';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100493397';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA101208838';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043885';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871810';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079127';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132929';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871831';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578629';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871835';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704392';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704402';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031838';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578630';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578631';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704405';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704426';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871843';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079128';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043886';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217311';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100087618';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132931';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA101208905';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132932';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100108669';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100493398';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA101208927';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132934';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079129';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100087619';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031839';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704429';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100108670';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704435';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031840';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217312';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031841';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043887';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031842';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578632';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100031843';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100493399';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA101208943';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043888';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217313';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100108671';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100132939';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871865';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704470';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100032021';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079130';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100871870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704489';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079131';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079132';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA101208996';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217314';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA101209005';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100578634';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100108672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100079133';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100043890';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100217315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:41' WHERE product_id = 'CMA100704495';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100493400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704499';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100108673';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100132940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100079134';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704501';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100217316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704516';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871917';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100087620';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100032022';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100043891';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA101209017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100079136';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100032023';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA101209090';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100578637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704520';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100217318';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871945';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100108674';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100043892';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100493401';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100132942';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871959';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100132943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871962';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100217319';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871965';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704551';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100493402';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100032024';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871975';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100132945';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA101209126';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100217320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100079137';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100871979';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100872005';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100872007';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100087621';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100217321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704571';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100493403';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100108675';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100079138';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100872009';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100872014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100578639';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA101209218';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704576';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100132946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100704638';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100043893';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:42' WHERE product_id = 'CMA100493404';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100578641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100872024';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100493560';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100872032';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100087622';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA101209232';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100043894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704646';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100217322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704670';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100132950';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100108676';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100032025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100079139';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100217323';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100578642';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100217324';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100872034';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100132951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704676';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA101209263';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100108678';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100087623';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA101209296';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100493561';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100108803';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100217325';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100132953';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100032026';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100872112';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100079140';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100087624';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100578645';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA101209346';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100043895';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100578646';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100079141';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100872114';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA101209374';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100493562';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100108804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704695';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704698';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704703';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100087625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100079143';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100132955';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA101209379';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100043896';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100032027';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100108805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704718';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100872124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704725';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100217326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704745';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100704753';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:43' WHERE product_id = 'CMA100108806';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100217327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100132956';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043897';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100493563';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100578648';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA101209403';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100087626';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704766';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032028';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100132957';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100087627';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872138';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100578649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100079144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704781';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100493564';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100217328';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100108807';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043900';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872154';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100217329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100079145';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100087628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704786';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA101209494';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704789';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872167';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100493565';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100578650';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100493566';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100132958';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032029';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043901';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100132959';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032030';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100578653';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872181';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100108810';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872187';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872199';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872202';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872206';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872216';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032031';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872220';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100493567';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032032';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704792';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032033';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100493568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100079146';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100132960';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100217330';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704814';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100087629';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704816';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100087630';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100217331';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100087631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704829';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704852';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043902';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA101209499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704854';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA101209584';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100108812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100079147';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100032035';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100087632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100133155';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872226';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872278';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100578655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704881';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872298';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872301';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872307';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043903';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872310';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872322';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704883';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043904';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100108815';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100043905';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100493569';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA101209607';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100578660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704941';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704947';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100872392';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:44' WHERE product_id = 'CMA100704952';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872398';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100108817';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100108818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079149';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704964';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704975';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100043906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704978';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704983';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872411';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100108819';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100108820';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100108821';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217333';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578802';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109264';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493570';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA101209632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133156';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872428';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872437';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704985';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872441';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872448';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704986';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704987';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872453';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100704999';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100087633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705006';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705008';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705011';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705020';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872503';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872505';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872510';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578803';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872514';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705096';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705098';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872517';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705110';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872520';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705123';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705143';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA101209665';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872522';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872524';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872527';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705145';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705165';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872545';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109360';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705181';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079150';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100043907';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217334';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493571';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032038';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217335';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705189';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705197';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872548';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079151';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032039';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493572';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109361';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044067';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493573';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872571';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872588';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705199';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872597';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872623';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872624';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872625';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872627';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA101209813';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872633';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578808';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578809';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032040';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493575';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109362';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA101209915';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705249';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032041';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705250';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109363';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133168';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079152';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705252';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705254';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705258';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705260';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705263';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705268';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705271';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044070';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872667';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872691';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872697';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217336';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100087634';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705273';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872707';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872712';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872715';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100087635';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705276';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872726';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493576';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872728';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705283';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872730';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872733';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872741';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044072';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705285';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578812';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079155';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109367';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705291';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA101209942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705297';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044073';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133170';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044074';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872745';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872757';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872766';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872771';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705299';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044075';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109368';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872774';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872799';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872804';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872828';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872832';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872835';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872839';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872840';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032042';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217338';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109369';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872842';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872844';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578817';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705334';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872848';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872851';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705337';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100087636';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA101209987';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217339';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493578';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872855';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079156';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133172';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493579';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100578820';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109370';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109371';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044076';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217340';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044077';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705391';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705412';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705421';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100087637';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872883';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872885';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705427';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109372';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872896';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705451';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705461';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705468';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705474';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872903';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705479';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100109374';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705483';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705486';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872920';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA101210030';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100493580';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100079157';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100032044';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100133174';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100044078';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100217341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705508';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705512';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705516';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100087638';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100872941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705524';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:45' WHERE product_id = 'CMA100705526';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705554';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705583';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217342';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705611';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109424';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA101210134';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100079158';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100872947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133175';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578833';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493581';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100044079';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133176';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109426';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133177';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578834';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493582';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578837';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100044080';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100044081';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032045';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100079159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705613';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA101210154';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100872996';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873006';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873027';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873030';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873033';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873038';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032046';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705621';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873046';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873053';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100087639';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873062';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873075';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705647';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873101';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873114';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873116';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705675';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705680';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA101210166';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578854';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217344';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109427';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493583';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032047';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100079160';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705684';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100044082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873130';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133181';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873149';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA101210230';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100087640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705689';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217345';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873155';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100079161';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705709';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873176';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873181';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873188';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109428';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578857';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705723';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100044083';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705725';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705728';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873202';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873209';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705738';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705746';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873214';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873217';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873219';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100087641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873222';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100087642';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873225';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705754';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873230';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873236';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705765';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873241';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873243';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873244';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705791';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705798';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705813';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217347';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873259';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873267';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873270';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873273';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873275';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493585';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873284';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578955';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217348';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100079162';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109430';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100044084';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133185';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705838';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705848';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109431';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032255';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109432';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109433';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873294';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA101210258';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133189';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217349';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705859';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873312';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100087643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705885';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873317';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705896';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873330';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873337';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705924';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109434';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100079163';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705979';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705981';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705983';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705994';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873371';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873382';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100705998';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100706003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100706004';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578958';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA101210293';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA101210356';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100044085';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493587';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493588';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100217350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100079164';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100032257';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493589';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100578961';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100493590';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873396';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873434';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873440';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100133192';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100109435';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873451';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:46' WHERE product_id = 'CMA100873473';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217351';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873478';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706006';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100087644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706024';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706046';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873522';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217352';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100079165';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA101210413';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706057';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706059';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493592';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706060';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873555';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873584';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA101210424';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044086';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109437';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032258';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044087';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217353';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578963';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217354';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873587';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873593';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873601';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873608';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706074';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133197';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493593';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA101210431';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133200';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873615';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109440';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493594';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873635';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100079166';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873650';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873668';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873680';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100109442';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133202';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032259';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100087645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706136';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100493595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706157';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706158';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044089';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100087646';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044090';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706159';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100706167';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578967';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217355';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA101210517';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873682';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873687';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873688';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873689';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100578970';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA101210530';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA101210555';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100087811';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100044091';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100079167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873691';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100873701';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100133207';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100217594';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:47' WHERE product_id = 'CMA100032260';
COMMIT;
