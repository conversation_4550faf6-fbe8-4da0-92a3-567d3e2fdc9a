BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363404';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363416';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715202';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882136';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100494836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100356447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715235';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715236';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363446';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882144';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045768';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080986';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100088530';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100494837';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882146';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882147';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100356448';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715246';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073142';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045772';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882154';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882165';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045773';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882166';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882172';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882174';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363517';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882180';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882184';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080987';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100494838';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363529';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363536';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100088531';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715287';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715306';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100494839';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715310';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715332';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715339';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715348';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363543';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363553';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715367';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363574';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987419';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363582';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987461';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987468';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987472';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045774';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363665';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363668';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882196';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882210';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882212';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882214';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363674';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882215';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882216';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363718';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363724';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715426';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715429';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715440';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045899';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882219';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100494840';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363745';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363765';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100088532';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363767';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100496179';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045901';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045902';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080990';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100496180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715445';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715527';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715533';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045903';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987479';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100088533';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073231';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100496181';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363824';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987488';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363825';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363877';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882222';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363901';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882224';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101363910';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987543';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882226';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882253';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882254';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882256';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987557';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364016';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364018';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987573';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364058';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987589';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987651';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882261';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364077';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715569';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080992';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100088534';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073252';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045904';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364141';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364160';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100496182';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100987673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715585';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364164';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364181';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715634';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715657';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364251';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364258';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882270';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715671';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101073268';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364268';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715715';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100080993';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364281';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882276';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045906';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100882283';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100045907';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100496183';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA101364297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:10' WHERE product_id = 'CMA100715719';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364310';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882294';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364399';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715728';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715819';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715867';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100080994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715871';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496184';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987695';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715875';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715890';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987731';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882313';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715895';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715898';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088535';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882318';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987742';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045910';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073288';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073316';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987744';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364436';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364466';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364490';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045911';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364509';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715904';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364514';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715913';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715919';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364541';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987749';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987771';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364552';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364614';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987774';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987788';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364626';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987798';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987802';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987809';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364652';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715939';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073322';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100715998';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364660';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364677';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496185';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987826';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045912';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364683';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100080995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716043';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987843';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987849';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987853';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882332';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987859';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882338';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882339';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882342';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882354';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088536';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882356';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364763';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882360';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882367';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882370';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882371';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882377';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882379';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882387';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088537';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496186';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073388';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987873';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496187';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364774';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716077';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045913';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100080996';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716188';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073450';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987882';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716203';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073456';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882393';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073458';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073471';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073474';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073478';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882401';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882403';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716207';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987888';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987902';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716336';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073493';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716402';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716405';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882409';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073600';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987910';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987913';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987921';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088538';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987925';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882416';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100080997';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073619';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496188';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882422';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882428';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882439';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716407';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987959';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882440';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073644';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882448';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073650';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882450';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987987';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882452';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364885';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364946';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100987995';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882460';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882466';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882476';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364980';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988027';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988031';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073658';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364983';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073668';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882477';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364993';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988037';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882484';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364996';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101364999';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365002';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365006';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365017';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882495';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882496';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882498';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365019';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988042';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073680';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365038';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988065';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073687';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988068';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365039';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073694';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882505';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365046';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882509';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365049';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073699';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988070';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365052';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365054';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365057';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365065';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365069';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365084';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365090';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365092';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045914';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716417';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988082';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988091';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088539';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365102';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365108';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496189';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365112';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365118';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716477';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073710';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882511';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882515';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088540';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365122';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365131';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882517';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365135';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365144';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988098';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365148';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100080998';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365153';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882522';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882527';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882528';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882534';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100080999';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988121';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365158';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882536';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073717';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073735';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073744';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716501';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365214';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365225';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073821';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365228';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882537';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882547';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073837';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882550';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882554';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716545';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365238';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365301';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365310';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882565';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365323';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365333';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882571';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365347';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882577';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716629';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716679';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365369';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716681';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365387';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882578';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716686';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882605';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882609';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882614';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496190';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365390';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365399';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365406';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100716717';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101073941';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988128';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988173';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100496191';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365418';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988183';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100081000';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045916';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988189';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088541';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100045917';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100988192';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA101365434';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100088542';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:11' WHERE product_id = 'CMA100882616';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365443';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100045918';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365445';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365457';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365465';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365467';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365471';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365475';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988198';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988279';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074013';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100081001';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365481';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100045919';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074062';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882620';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882640';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988285';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882641';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882643';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365492';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365496';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988343';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365500';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365516';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365519';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496192';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365521';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496193';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365525';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882650';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988357';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365529';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882667';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365540';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365546';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882671';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365548';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882678';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988382';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365550';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882681';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988386';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100081002';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074067';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988394';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365566';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074079';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365575';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365583';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365586';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882685';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882693';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882702';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100045920';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496194';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988397';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988452';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496195';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716857';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882707';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882711';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882713';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882716';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100088543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716861';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882718';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716942';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882726';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716944';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882734';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365596';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365610';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716945';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100081003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716961';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988457';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365627';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074097';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496196';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882795';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074111';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100045922';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365646';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074123';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988466';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988509';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716964';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988547';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100088734';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365671';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882799';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365680';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882800';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100045923';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882807';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882812';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882814';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882816';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882820';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365683';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100716987';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882824';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882835';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882839';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365700';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717007';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365712';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074134';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074141';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882844';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365723';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074157';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365729';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717078';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365732';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365739';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074264';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365747';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074271';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717087';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365755';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074330';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365783';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365790';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365800';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365807';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365822';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717119';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717132';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365838';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882866';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717165';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100081004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717187';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717190';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365844';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365869';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365872';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988551';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365875';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717221';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882873';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882882';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074391';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882895';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074397';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074399';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496197';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074400';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882896';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074416';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074431';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100045924';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100045925';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100088735';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365882';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365932';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074442';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717258';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365933';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365936';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074484';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365938';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717282';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074498';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882902';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717295';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717363';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882920';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717366';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365943';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717393';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365966';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717402';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717408';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717409';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365992';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365993';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365995';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365997';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101365998';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366000';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882932';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366001';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717438';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717449';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100088736';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882940';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882946';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100081005';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100046102';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074533';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717459';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074545';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074550';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496198';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074556';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882950';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717467';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717474';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988592';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717506';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717526';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366021';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496199';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717532';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366028';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366033';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496200';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988627';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717569';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366035';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717574';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366053';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100088737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100717577';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366059';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366077';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366082';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366091';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100496201';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366095';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366101';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366104';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101074578';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366107';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366109';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366110';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988691';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988744';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366111';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882951';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366124';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366150';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882968';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366168';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366173';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366179';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366185';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366190';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366195';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988803';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366197';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366200';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366204';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100882992';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366206';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100988831';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366227';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366230';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366232';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100883004';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366236';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366270';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100046103';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100883011';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366272';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366291';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366294';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366307';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA101366315';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100081006';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:12' WHERE product_id = 'CMA100081167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717594';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988853';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717612';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717613';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074615';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883031';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883041';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366321';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883045';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074625';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717625';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074631';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883054';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496202';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074636';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074651';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883060';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074652';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074654';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074655';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988866';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074658';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988884';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883063';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988892';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074661';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883067';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366341';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366346';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366347';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988922';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988947';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988952';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366349';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717630';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988956';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717661';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074681';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988961';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988963';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074689';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883073';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074692';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717680';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988965';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883094';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883098';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717712';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883107';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717766';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883111';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717783';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717785';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717797';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883113';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883119';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988978';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717830';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883127';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883135';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100988982';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883146';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989008';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989019';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717846';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989039';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989049';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989053';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717883';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989072';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989093';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883164';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074706';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883166';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088739';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046105';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717887';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717897';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046106';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717901';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989098';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074738';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989111';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074752';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717936';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074762';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717937';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366382';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074775';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883174';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883177';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883179';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883180';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883182';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883184';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883185';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100081168';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074779';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074785';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100081169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717945';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989171';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496204';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883188';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883196';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883198';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074787';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074843';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074844';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074845';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088740';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074846';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366429';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366458';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100717987';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366462';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718002';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366464';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718011';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101366468';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074847';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883199';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883212';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718021';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074900';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883228';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074909';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046107';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074928';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883230';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883257';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100081170';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883258';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883265';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074932';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883267';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074941';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883271';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046109';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101074943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883275';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088741';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989173';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496205';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989176';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100496206';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718048';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989181';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883283';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075078';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100088742';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989199';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718155';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718158';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718196';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075118';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100718200';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075146';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA101075148';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100046110';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883308';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883311';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883314';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883317';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989207';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883322';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100989212';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883326';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883330';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883333';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883336';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883339';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883342';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:45:13' WHERE product_id = 'CMA100883346';
COMMIT;
