BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107701';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086630';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100362994';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100103469';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130269';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100861428';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107707';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107711';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107715';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100042520';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100861431';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100086631';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100130270';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100012524';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100200072';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100103470';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100096827';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA101107718';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:13' WHERE product_id = 'CMA100077960';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100490061';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100086632';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100497316';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA101107725';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100096828';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100861444';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100103471';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100012525';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100200073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100077961';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100042521';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA101107729';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100086633';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA101107736';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA101107741';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100497564';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100042522';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100096829';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130272';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100200074';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100012526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100077962';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100086634';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100490062';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100103473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100362995';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA101107746';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130273';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100861455';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100012527';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100012528';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100042523';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100200075';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100096830';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100490063';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100086635';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA101107751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100362996';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100861472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100077963';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130274';
UPDATE products202503 SET brand_id = 'CEB001224', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA101107775';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100103474';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100042524';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100012529';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100497567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100077964';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100200076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130275';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100362997';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100861702';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100096831';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100490064';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100103475';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100012530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100362998';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100086636';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130279';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130280';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100103476';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100042525';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100200077';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100012531';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100497568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100077965';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100103477';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100861718';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100200078';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100096832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100362999';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100130281';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100086637';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100861781';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100497569';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:14' WHERE product_id = 'CMA100042526';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100103478';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100200079';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861783';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100086638';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077966';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100497571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363000';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100096833';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100042527';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100130283';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861816';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100103481';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100130286';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100200080';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100096834';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100012532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077967';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100497810';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100042528';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363001';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490066';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861825';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100012533';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490067';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100130288';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100012534';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100096835';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861845';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861847';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100497813';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100086639';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100497818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077968';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100200081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363003';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100130289';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100086640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363005';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100012595';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100497820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363006';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100096836';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100042529';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363007';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498024';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100200082';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490068';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100103482';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861953';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363008';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077970';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498027';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363009';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498036';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100012596';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100103483';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363010';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100012597';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100042530';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100200215';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077972';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490070';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861961';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100130290';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100103485';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498220';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100861992';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100086641';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100012598';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490071';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100096837';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862001';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498224';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100042531';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100130292';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862014';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862015';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100200216';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100086642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363011';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862027';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498230';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100103486';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100130293';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100363012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100077973';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100498234';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862034';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862040';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862044';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100490072';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100862049';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100096838';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100042532';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100103487';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:15' WHERE product_id = 'CMA100200217';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862059';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862064';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130294';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077975';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862075';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498238';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103488';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498243';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490073';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096930';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862114';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200218';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363017';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012599';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363018';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130326';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200219';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077976';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100086643';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103711';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498403';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363019';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490074';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498404';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490075';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042534';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498405';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490076';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200220';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498419';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012600';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100086644';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498422';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077977';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862124';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200223';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490077';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096932';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490078';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363164';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862159';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862170';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042535';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862181';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862191';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130464';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862208';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042536';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012601';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077978';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012602';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130465';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012603';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103713';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096933';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103714';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490079';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103715';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077979';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042537';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490081';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096934';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498426';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103716';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498434';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862226';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862228';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862236';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498435';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130466';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498440';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862251';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862261';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200224';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862288';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096935';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130467';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130469';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100086645';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100086646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363165';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862290';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130470';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077980';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363166';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862298';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363167';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862315';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490082';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862324';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130471';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862342';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130472';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130473';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490083';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130474';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012604';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363169';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096937';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077981';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012605';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498442';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200225';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498443';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100086647';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498447';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200226';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100498451';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862346';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200227';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862391';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200228';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200229';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862393';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862398';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862404';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200230';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200231';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100564235';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042539';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130477';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103846';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100564237';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103848';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096938';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100086648';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862609';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100862614';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100042540';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100564387';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100130480';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100096939';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100103850';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100564388';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100490084';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100012606';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100077982';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100200232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:16' WHERE product_id = 'CMA100363170';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490085';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012607';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012608';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200233';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100862627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363171';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100862799';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363172';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042541';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077983';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077984';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564390';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200234';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077985';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363173';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042542';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096940';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012609';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103852';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200236';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103854';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130662';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012610';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100862935';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100086649';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096941';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200237';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103856';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564394';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042543';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077986';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363176';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130664';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042544';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130665';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863074';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103857';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564396';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863211';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130666';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042591';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564398';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077987';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863240';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863246';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863260';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863272';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863275';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863287';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863294';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863298';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863311';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490255';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042592';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100086650';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103858';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564534';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363178';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200238';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096944';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200239';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564535';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863316';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863352';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012611';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863358';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863372';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863379';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863380';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490256';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863381';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863382';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103860';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200240';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130668';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863383';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363180';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200241';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130669';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490257';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103862';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564542';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863393';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863415';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042593';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863424';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863427';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863442';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103928';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863444';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103930';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077989';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490258';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863490';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363182';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103931';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042594';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096946';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100086651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130670';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490259';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077990';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130672';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564549';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100103932';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130673';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012612';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130675';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200242';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042595';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100200243';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863509';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077991';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863526';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863544';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863557';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100077992';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012613';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100130676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100363184';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863566';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863575';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012614';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100086652';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100564583';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012615';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012616';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100863584';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100490260';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096947';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042596';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100012617';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100042598';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:17' WHERE product_id = 'CMA100096948';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100564584';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100077993';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863595';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100490261';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100202677';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100012618';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130677';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100490262';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863612';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100086653';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100363185';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130679';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100202678';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100096949';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100077994';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100012619';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863639';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103936';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103937';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100086654';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130680';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100042599';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100490263';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100363191';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100564588';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863648';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100202679';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863660';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863662';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863669';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863676';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100012620';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863685';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863700';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130681';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130682';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130683';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100086655';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130684';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103938';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130685';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100012621';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100564589';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100012622';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100077995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100363372';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100042600';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130686';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100490264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100363377';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100042601';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100086656';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103940';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130687';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100202680';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863717';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863730';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100096950';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130688';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100490265';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100564792';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130689';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100490266';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100363379';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100202681';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100086657';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100012623';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863741';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100077996';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100042602';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100096951';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100103941';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100490267';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100130690';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100202682';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100863750';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:18' WHERE product_id = 'CMA100096952';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100042603';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012624';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863845';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863865';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100103942';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202683';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202684';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100077997';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100086658';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100103943';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100042604';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100363381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100077998';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100103945';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100096953';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490268';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202685';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202686';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100130870';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490269';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490270';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490271';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863878';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100086659';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100077999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100363384';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564794';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012812';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564798';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100042605';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100078000';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100130871';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100104095';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100096954';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202687';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490272';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100130873';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202688';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100042606';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564799';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863891';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100104096';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012813';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100130874';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564800';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012814';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100096955';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012815';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012816';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100042607';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100130875';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490273';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100078001';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012817';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100042608';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863903';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863922';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863934';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863947';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100086803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100363388';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100042609';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564801';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100104097';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100078002';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100012818';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202689';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100078003';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100096956';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490274';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863960';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100202690';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490275';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564808';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100863976';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100363389';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100564809';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490277';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100864031';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:19' WHERE product_id = 'CMA100490278';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042610';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086804';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864038';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864052';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864069';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104099';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086805';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864086';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100130876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100363390';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864100';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012819';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100564810';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100096957';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490279';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012820';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202691';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104100';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490280';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100130879';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100363391';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100564811';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864133';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012821';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100130881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100366297';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104101';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100564812';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012822';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042611';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570900';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864161';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570901';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864179';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864188';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864204';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078179';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086807';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100130891';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570903';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864210';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042612';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490281';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202692';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086808';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490282';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100366300';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104102';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100096958';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078180';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570905';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864230';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104103';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042613';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012824';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042614';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490283';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864269';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864287';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864295';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864304';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864308';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104104';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864310';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078181';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012825';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202693';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012826';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570908';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086809';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100096959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100366303';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104105';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864342';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490284';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042615';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864381';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864383';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864406';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100130892';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864407';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202694';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570910';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104106';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078182';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570911';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104108';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042616';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100096960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100366304';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042617';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086810';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490285';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202695';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570913';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078183';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202696';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012827';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078185';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100130894';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012828';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864423';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012829';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104110';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012830';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864433';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864454';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864459';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104111';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012831';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104112';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100012832';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100096961';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100096962';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100078186';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100570966';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100490286';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100366309';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202697';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042618';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202698';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864465';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100202699';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100104113';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042619';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864468';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864473';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100042767';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864475';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100864480';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:20' WHERE product_id = 'CMA100086811';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490287';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490288';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100570969';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086812';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864486';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202700';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490289';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104115';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202701';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490290';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202702';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086813';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042768';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104312';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864491';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864501';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864502';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864504';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864506';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131064';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864508';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366316';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864515';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366317';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097077';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104314';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366321';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864551';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864563';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864568';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104316';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100012833';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104317';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100570972';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490291';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131065';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490292';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086814';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100078187';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104318';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864575';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864583';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100012834';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864595';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104319';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042769';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100570975';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097078';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100078188';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086815';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100012835';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100012836';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366323';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490293';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131067';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013003';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366324';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490294';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013004';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490295';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097079';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202704';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013005';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086816';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490296';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013006';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013007';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131068';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013008';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100570980';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864608';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013009';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104323';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013010';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104325';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864664';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864671';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097080';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042771';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104327';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202705';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490297';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086817';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202941';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131069';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490298';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490299';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100078189';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490300';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490301';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490302';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366325';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013011';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864678';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864689';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864696';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864698';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100571183';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100366326';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097081';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086818';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490303';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086819';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202942';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864701';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100078190';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864711';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104329';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131071';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131072';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864714';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864727';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864743';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013012';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131075';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864747';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131076';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864770';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864780';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131078';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100399340';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100078191';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202943';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864784';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100571184';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490305';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864787';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864790';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042773';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097083';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086820';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100399341';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490306';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100131079';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100571186';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202944';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100013013';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042774';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100104330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100399343';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100571188';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100042775';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100864800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100399344';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100202945';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100490307';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100097084';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100086821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:21' WHERE product_id = 'CMA100078192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131080';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571189';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202946';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490308';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086822';
UPDATE products202503 SET brand_id = 'CEB000400', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100097085';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100104331';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013015';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399345';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571191';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399347';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100078193';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013017';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864808';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100399351';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013018';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864842';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864849';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864877';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042776';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100042777';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100104332';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202947';
UPDATE products202503 SET brand_id = 'CEB001040', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100202948';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100104333';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490309';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100571232';
UPDATE products202503 SET brand_id = 'CEB002701', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100490310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100131081';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086823';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013019';
UPDATE products202503 SET brand_id = 'CEB000679', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100864878';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013020';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013021';
UPDATE products202503 SET brand_id = 'CEB000110', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100013022';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:44:22' WHERE product_id = 'CMA100086824';
COMMIT;
