BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736524';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388343';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362164';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362198';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092298';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362199';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092357';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362206';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362215';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092379';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002342';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362217';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002379';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362219';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362239';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736525';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092386';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736528';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362240';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736545';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388355';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092388';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388358';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736549';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388437';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362256';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362269';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388467';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362271';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082354';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388481';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362305';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362345';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736570';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736609';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002400';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002444';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100089591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736619';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092401';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736622';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002464';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002471';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100082355';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388581';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388588';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101388600';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101092403';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101362357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736624';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA101002535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:42' WHERE product_id = 'CMA100736643';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388641';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100089592';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100082356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002616';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002628';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002637';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092413';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362368';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362384';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736650';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388650';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736669';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100082357';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092421';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092427';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736681';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092432';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100089593';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092436';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362395';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092438';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002660';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002661';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092443';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092466';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092474';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092477';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736699';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362434';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002686';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362436';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002776';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092481';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092484';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388673';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092487';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092493';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092495';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100089594';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092500';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092506';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736701';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100082358';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388703';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388705';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092521';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092523';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092528';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092541';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092549';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092552';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362448';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388725';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092553';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092558';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092563';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362459';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362482';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362499';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362517';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092565';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092574';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092590';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002812';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002854';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092593';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002861';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092603';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002863';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092607';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002866';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388740';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100082359';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002884';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092620';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092625';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092630';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388756';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362522';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362535';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002889';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388784';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092635';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362555';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362568';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388797';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388800';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362576';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388807';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362590';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388812';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092642';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002890';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362603';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092644';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736786';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101092664';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362642';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002894';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736790';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736796';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100736800';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388869';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002913';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002920';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100082360';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388910';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388933';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101362735';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002933';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002944';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA100089595';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388956';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388981';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002958';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002963';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002976';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002979';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002981';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101002989';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101003000';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:43' WHERE product_id = 'CMA101388985';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092667';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092675';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101362946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736805';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082361';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089596';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003009';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003014';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092678';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092680';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092688';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389090';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389098';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363015';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092692';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736895';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092696';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389119';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092703';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089764';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736914';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092749';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736917';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736921';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736925';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736928';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736946';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736948';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736960';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092794';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736975';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736984';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092799';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082362';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736986';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092817';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736994';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082363';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092825';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092828';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100736998';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092835';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092838';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092849';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003043';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092861';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082364';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092865';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092870';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003051';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092872';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003074';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092877';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003077';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003080';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003085';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363069';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003091';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363152';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092878';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092881';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092885';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003111';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092895';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092900';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363161';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363184';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363193';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092919';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363202';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003127';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092935';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092946';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092949';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737008';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389175';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363209';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092955';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389313';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003162';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003170';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363234';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737058';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363299';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737070';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082365';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363344';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092956';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092960';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092973';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082366';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389315';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003172';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101092997';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363357';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093006';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093012';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737126';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737138';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089766';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363381';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363401';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093015';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093038';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093040';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093045';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093053';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101363524';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093056';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737139';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093061';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737153';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737155';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737156';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737160';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101364443';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389318';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101364450';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389340';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003190';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003203';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003206';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389362';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082368';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737163';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737174';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389419';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101364471';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093099';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101364631';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737179';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737187';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101364635';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737189';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003213';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101364673';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003288';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101364818';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089767';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093102';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101365031';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003292';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003297';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003300';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101365130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737214';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737223';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093115';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003316';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003381';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093128';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093130';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093135';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101365317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082369';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093137';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389504';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389586';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003474';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737229';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389609';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093143';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389662';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003479';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101365427';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389680';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389726';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093153';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003504';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737240';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737255';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093166';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737263';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003528';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737269';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003531';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003534';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389756';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389801';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093195';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003545';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082371';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101365748';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003567';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003574';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003577';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003582';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003583';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389813';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093223';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093237';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093239';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093254';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100082372';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093257';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101365949';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093273';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093275';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100089769';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737272';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003607';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003611';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003621';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101003622';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093293';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093296';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101389859';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737299';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093300';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA100737348';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093322';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:44' WHERE product_id = 'CMA101093348';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093356';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101389870';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093366';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093370';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737368';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737379';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101389884';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737385';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100089770';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101366136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100082373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737476';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003626';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003666';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093399';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093413';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093415';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101366261';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003686';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100089771';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101389903';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100082374';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093419';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093440';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101389917';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093445';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100082375';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100089772';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093463';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101366352';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093465';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101366885';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101389924';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093534';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101389957';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101389978';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003727';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390016';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101367164';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093547';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390035';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737485';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101367422';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390068';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003732';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003741';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003746';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003750';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390094';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390103';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101367477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100082376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737515';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003754';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003767';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003778';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093551';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003782';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737524';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003804';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003811';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093564';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093573';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003817';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003874';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003881';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101367580';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390114';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101367761';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100082377';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390132';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003902';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737550';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390214';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737564';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737572';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093582';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100089773';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390289';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390293';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003968';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101367893';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100089774';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101003985';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004003';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737587';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390343';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390349';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390372';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737602';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100082378';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390417';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737620';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737667';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737678';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101368780';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004005';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093703';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004020';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390525';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100082379';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101390551';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737681';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004056';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004060';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004068';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093709';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093714';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093717';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737742';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737749';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093734';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093806';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737754';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093827';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093910';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100089775';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737828';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093933';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737834';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737841';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737850';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101093948';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004081';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004101';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737858';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA100737880';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:45' WHERE product_id = 'CMA101004129';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082380';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100737893';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101093954';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101093968';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101368900';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101093971';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101093974';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390591';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089776';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004151';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004180';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082381';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101369133';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100737902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100737930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100737959';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100737960';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390618';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101093977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100737961';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101093993';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004182';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738016';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738025';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390630';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738032';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094066';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004201';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738035';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390662';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738051';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004257';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004279';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390710';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004286';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094096';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094121';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738061';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004300';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094127';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390746';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390782';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094135';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094144';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094146';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094149';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094152';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101369442';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094158';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738062';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094160';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094163';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094170';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094174';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094185';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101369468';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101370295';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089777';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004309';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390892';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094190';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738067';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738069';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004333';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390971';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390985';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101390989';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094199';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004345';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738078';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738112';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738116';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094221';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094227';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094237';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391008';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738136';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094238';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094269';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094273';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094276';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094279';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738146';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094283';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094291';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094296';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094298';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738148';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738190';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101370943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082384';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089779';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004398';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391034';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391076';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004425';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094311';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094322';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391092';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094328';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094332';
UPDATE products202503 SET brand_id = 'CEB002910', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101371824';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004451';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391105';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738193';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004454';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738234';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738237';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094333';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391117';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094340';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738246';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094355';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391126';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094358';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391145';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738249';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738259';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738261';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094375';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094384';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094385';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391177';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391191';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004509';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738272';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391242';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094387';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094390';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738277';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094396';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738284';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738287';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094404';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094415';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391346';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738299';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738304';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004542';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738332';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738343';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738346';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094438';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089780';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004555';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004603';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004605';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004630';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089781';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094441';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004637';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391349';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738389';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004738';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391428';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391446';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738396';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391476';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094462';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391517';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094471';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738480';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738623';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391527';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738625';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082386';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094478';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738647';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094499';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094517';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738663';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738685';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391563';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391579';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738686';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738736';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391591';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004768';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004790';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738742';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094535';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089783';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094538';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738798';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004810';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004826';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391653';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082387';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089784';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738846';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089785';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094574';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004828';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094583';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094588';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094590';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391800';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004850';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004865';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004890';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094591';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094627';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094630';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004899';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004917';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094637';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004922';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738895';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094649';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094656';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094658';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004923';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738928';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004942';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004965';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004966';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094664';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738929';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004976';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101004983';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101005000';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094667';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738931';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094676';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738949';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101005020';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738955';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094680';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094692';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101005030';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101005044';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094715';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094717';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094719';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094722';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101005048';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094733';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738976';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101005061';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094745';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094749';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391835';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101094754';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100082388';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738982';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100738984';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101005131';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100089786';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100739010';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA101391879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100739037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:46' WHERE product_id = 'CMA100739039';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005144';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391903';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094776';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391907';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739042';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391913';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739069';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739075';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094808';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082389';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094812';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739079';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094824';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094831';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094838';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100089787';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094841';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094843';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739121';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094845';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094906';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094910';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739124';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094916';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094922';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094937';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094939';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739225';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391938';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391952';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101094942';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095027';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391982';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101391996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739275';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101095037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005146';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005169';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082390';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739320';
UPDATE products202503 SET brand_id = 'CEB001895', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101392071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082391';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005186';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100082392';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005195';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005201';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA101005204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739345';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:45:47' WHERE product_id = 'CMA100739357';
COMMIT;
