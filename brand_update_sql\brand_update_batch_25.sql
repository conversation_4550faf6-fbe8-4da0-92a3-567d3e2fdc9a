BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023271';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023279';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751676';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023294';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103845';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023349';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751701';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103860';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751793';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751799';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023420';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103903';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103906';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103907';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103912';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751812';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103923';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119166';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103931';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751843';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751845';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103964';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103965';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103967';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023426';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751855';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100083327';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103973';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103975';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103977';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103982';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751865';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751884';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119168';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023544';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023576';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103984';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023586';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103990';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751915';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751917';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023774';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751925';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751927';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101103998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751929';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104007';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751930';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751935';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100083328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751945';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104009';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751947';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751954';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751969';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751989';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023890';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100083329';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104033';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023906';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100751992';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104043';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101023971';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024012';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752050';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024048';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104057';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752058';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104074';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104096';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752081';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119171';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100104282';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024070';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104099';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104106';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104111';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024109';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100104283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752105';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119172';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024140';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024154';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752131';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104121';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104123';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752141';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752146';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752155';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104136';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024181';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752156';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752208';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104187';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104200';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752213';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752244';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100104284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752251';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104246';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752307';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104296';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119173';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104309';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752329';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104334';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104347';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104353';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101104359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100104285';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100752336';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA101024289';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:04' WHERE product_id = 'CMA100119174';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024388';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024397';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024412';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104362';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024415';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104428';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104286';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752372';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024431';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119175';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752384';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104287';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024444';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024449';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752405';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752449';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752505';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024512';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752518';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752539';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024566';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024577';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024580';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024581';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024584';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104453';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104466';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752644';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752651';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104288';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752663';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024632';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752672';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104495';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119177';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104528';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104289';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104290';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024672';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752729';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024727';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024731';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024746';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024751';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024757';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024764';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104291';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752805';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024770';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024773';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024775';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104546';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104292';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024786';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024797';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104580';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104581';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024800';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104293';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104593';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104616';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104624';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104650';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752810';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104668';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119179';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024827';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104701';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752826';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104717';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104721';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104729';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752830';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119180';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752841';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024971';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101024987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104295';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104296';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025001';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752862';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752878';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104733';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104739';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104743';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025033';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025040';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119182';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752920';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752923';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104748';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752955';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752957';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100752958';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025062';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119183';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104765';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104777';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104783';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119306';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104784';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104298';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753065';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753162';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104299';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025069';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025111';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119307';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753166';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025206';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119308';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025220';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104799';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104806';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025254';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025273';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104812';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025291';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025304';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104825';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101104829';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA101025306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753354';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753356';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100104300';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100119309';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:05' WHERE product_id = 'CMA100753358';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101104856';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025314';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119310';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025341';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119311';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025348';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101104860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104301';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101104923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753484';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753496';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101104940';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104302';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753500';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025359';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025372';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025375';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753518';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025382';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025401';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105004';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025402';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105029';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025411';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753536';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753544';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025436';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105206';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025484';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104304';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105223';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753564';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753595';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753602';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105238';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753608';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025590';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119314';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025606';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105258';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119315';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025609';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119316';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025616';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753617';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025624';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104306';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753772';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753778';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753781';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119317';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105317';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753786';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753855';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753859';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105386';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753865';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119319';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105394';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104307';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104309';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025672';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025701';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105405';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025712';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025718';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025727';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753939';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105458';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025732';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025750';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025759';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104310';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100753969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754004';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025801';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025803';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119322';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105526';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105535';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754054';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754086';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754095';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105584';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754110';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754116';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754129';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104311';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754169';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754179';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025911';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105671';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754191';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754225';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105707';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754262';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754269';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101025931';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104538';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105751';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105760';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119324';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754292';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754312';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026073';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026078';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026084';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026089';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105768';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105818';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026096';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105835';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104539';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105845';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026115';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104540';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754319';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026136';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754402';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754404';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026180';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754408';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105861';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105883';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026215';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026222';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754417';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105887';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105888';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026230';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026252';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026264';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105964';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026269';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119327';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026275';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100754432';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100104541';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026311';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101026336';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA100119328';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:06' WHERE product_id = 'CMA101105980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104542';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119329';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754456';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026418';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026433';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754490';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119330';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026442';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106111';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104545';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754513';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026463';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754530';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754536';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106134';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026471';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026481';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026484';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026488';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754541';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026490';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106173';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754670';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106186';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106190';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026577';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104546';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119332';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106210';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754680';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754761';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026597';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754769';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026610';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104548';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106236';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026701';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026705';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026728';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026731';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106249';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106257';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026736';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106425';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026778';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104549';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026782';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754840';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026821';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119333';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026825';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754978';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026831';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106444';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026836';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100754988';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026850';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026857';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026861';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106459';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755004';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026865';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026868';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106501';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026871';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026930';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026935';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026951';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106533';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026953';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026967';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119335';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026983';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106606';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119525';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101026992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755125';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755127';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027059';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027066';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027105';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027130';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755130';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104551';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755134';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119527';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755204';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106618';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119528';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104552';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119529';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106628';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106708';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027199';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027203';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755208';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106786';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027213';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027246';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755216';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106820';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027255';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106826';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104553';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119530';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104554';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106857';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106871';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027314';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119531';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101106886';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755248';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755266';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755270';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104555';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119532';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027367';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027399';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027418';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755301';
UPDATE products202503 SET brand_id = 'CEB002596', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101107030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755312';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104556';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027521';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104557';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755351';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027529';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027563';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755372';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755374';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027602';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027606';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027620';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027629';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755378';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755396';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027672';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755398';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755477';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027736';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100119534';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755500';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027831';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100104559';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA101027852';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:07' WHERE product_id = 'CMA100755580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101027876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755595';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101027897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755612';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755619';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101027902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755623';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101027909';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755651';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101027988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755663';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755675';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755687';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755739';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028057';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755741';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119535';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028067';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028070';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755744';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104560';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755752';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028096';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028117';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119536';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028121';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028127';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028129';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755758';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755766';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755774';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028137';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755789';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119537';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755815';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119538';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755837';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028164';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755845';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119539';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755854';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028184';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755890';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104561';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755930';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028212';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755934';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028219';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104562';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755940';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755951';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028297';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119542';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028301';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028304';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028316';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119543';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100755953';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119544';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756025';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756088';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028361';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119545';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756101';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028415';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756107';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028425';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028464';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028467';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104565';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119547';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756184';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756200';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756228';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119548';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104566';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119549';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104567';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756279';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756287';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756290';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104726';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028647';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104727';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028673';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756296';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028693';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119552';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028696';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028722';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028726';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028734';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028738';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104728';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119553';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028782';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756303';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028811';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028831';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028832';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028838';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756316';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028850';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756326';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028907';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028914';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028917';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104729';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028933';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100119555';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100104730';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028951';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756330';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756379';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101028967';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101029021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756389';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101029052';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA100756402';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:08' WHERE product_id = 'CMA101029076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104731';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119556';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029108';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029172';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119557';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029185';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029192';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029195';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756535';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756537';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029206';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029257';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104733';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029272';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029284';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756539';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029291';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029303';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756598';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756607';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119560';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104735';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119562';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756619';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104736';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756627';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029341';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029387';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119563';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104738';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756825';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756828';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119564';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029411';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756833';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756846';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119565';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029434';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029441';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029443';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029447';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756905';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029451';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029460';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100756906';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104739';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119566';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029472';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029481';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029483';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757009';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104740';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757117';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757139';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119567';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104741';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029522';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029565';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029567';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029576';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029587';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029648';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757166';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119569';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757174';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104742';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029670';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757197';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757215';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029679';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104744';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029684';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029688';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119570';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104745';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029696';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029711';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757256';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757299';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029721';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100104747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757329';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757341';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119571';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119572';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100119573';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA100757353';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029737';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:09' WHERE product_id = 'CMA101029750';
COMMIT;
