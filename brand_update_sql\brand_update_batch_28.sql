BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040440';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120897';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040473';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040476';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120898';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767656';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108596';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767706';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040507';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040520';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040524';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120899';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040537';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120900';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108597';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040555';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040569';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767760';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040595';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120901';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108598';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120902';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767770';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120903';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040625';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120904';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767777';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040640';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040650';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040652';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040667';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040681';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767786';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108599';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767794';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767849';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767852';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767858';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040737';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120905';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767898';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767903';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040740';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767909';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040749';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040755';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767924';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108601';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040758';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040778';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100767991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100768008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100768019';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108602';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100120908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100108603';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100768030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA100768046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:21' WHERE product_id = 'CMA101040801';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100120909';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA101040811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100108604';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768055';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA101040826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768061';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768103';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768111';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA101040832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100108605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768143';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA101040847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100108606';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768204';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100120910';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA101040857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768215';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768291';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100120911';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768293';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA101040880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:22' WHERE product_id = 'CMA100768323';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120912';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040888';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768329';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040899';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040910';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120914';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100108607';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120915';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768384';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040923';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040943';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768406';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040978';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101040995';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041014';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120916';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100108608';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041054';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041059';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768419';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041060';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120918';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768422';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100108609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768425';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120919';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041065';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100108610';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768444';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100120920';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041088';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA100768460';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:23' WHERE product_id = 'CMA101041117';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100120921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041120';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041126';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768462';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768519';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100108611';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768520';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768521';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041141';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100120922';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100108612';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100120923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768524';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100120924';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100108613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768559';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100120925';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768563';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100108614';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768700';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127711';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100108615';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111614';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111615';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041271';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127712';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041282';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768782';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111616';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041285';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127713';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768799';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768821';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768824';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041306';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768898';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768908';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768924';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041320';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041329';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127715';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768929';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127716';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041340';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768979';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041350';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041355';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041366';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111620';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041393';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127717';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100768999';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041409';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100769043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100111621';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127718';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041436';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100769046';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041453';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041533';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127720';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA100127721';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:24' WHERE product_id = 'CMA101041541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769093';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769115';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769180';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769201';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041556';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041565';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111622';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041575';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041598';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769222';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127722';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041605';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769231';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041617';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041652';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041666';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041675';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041677';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769259';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769343';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041687';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041699';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127723';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041715';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041770';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769353';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041779';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769359';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041828';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111625';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041836';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041851';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041859';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041864';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769366';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041870';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769374';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769418';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041904';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111626';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769422';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769445';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769449';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769462';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041924';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041993';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111627';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101041999';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127726';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127727';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042002';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042036';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769484';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042046';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127728';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042064';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769587';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127731';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042074';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042089';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769612';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042113';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042129';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127732';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127733';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111628';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769672';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042134';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769685';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769688';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769689';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127734';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769693';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042168';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111629';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042193';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042237';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127735';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769704';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042249';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769708';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042288';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042307';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111630';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042321';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127736';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127737';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042341';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042360';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769736';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042365';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769754';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769763';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769765';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042383';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042389';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111633';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127919';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042393';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042413';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042417';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769802';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769804';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769817';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127920';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042420';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127921';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127922';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127923';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769832';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111634';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042443';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127924';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769839';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769852';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042468';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042471';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769855';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042565';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042578';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042582';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111635';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042593';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769857';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111636';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042616';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042628';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042639';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769866';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042643';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127925';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042661';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769888';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769909';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042697';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100769917';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100111638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA101042724';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:25' WHERE product_id = 'CMA100127926';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042741';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100769939';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100769949';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100769970';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100769976';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100769986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100769989';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100769995';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770002';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111639';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770008';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770065';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770078';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770079';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770092';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770112';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770143';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770150';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770153';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770162';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770163';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770181';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770185';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042845';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042857';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127930';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111641';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042873';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127931';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042902';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042921';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042931';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101042942';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770188';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770190';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043001';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043020';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770208';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127933';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127934';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127935';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043035';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043043';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127936';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770215';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127937';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770241';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770265';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111866';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111867';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043056';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043100';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043108';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043121';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111868';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770295';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770314';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127938';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127940';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043129';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127941';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043149';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770397';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770409';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111869';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043159';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043170';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043183';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127942';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770539';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111870';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127944';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111871';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043190';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770557';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043242';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043261';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043264';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043278';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127945';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043356';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100127946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770671';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111873';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043394';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770719';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128074';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043403';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128075';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770741';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128076';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770782';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043452';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128077';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043463';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128078';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111874';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770815';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111875';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111876';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770828';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770860';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043526';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770868';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770874';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043546';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111877';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770881';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770908';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770927';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043554';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043572';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770937';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128080';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770948';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100770976';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128082';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043592';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043611';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043620';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043622';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111879';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771001';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043627';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043640';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771049';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043653';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043659';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043665';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771062';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771063';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771068';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771073';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771074';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771091';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111881';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043701';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043729';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128083';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043737';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100128084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771093';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043746';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043793';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771193';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771216';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771228';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771231';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100771239';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043803';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043817';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA100111882';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:26' WHERE product_id = 'CMA101043824';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043835';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043851';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111883';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111884';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128087';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043863';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771301';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043872';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771324';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128088';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128090';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111885';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771328';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771332';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043918';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111886';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043932';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043950';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043959';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111887';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043965';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128091';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101043977';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044000';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771335';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128092';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044006';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771352';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771357';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044011';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044019';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128093';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044021';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771466';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044028';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044047';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771509';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044049';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771537';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771583';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111889';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771594';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044110';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771602';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128095';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771611';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044125';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128096';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044151';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044167';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044176';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771615';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128097';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771616';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771624';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771635';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044224';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044238';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044243';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771638';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044255';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771659';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044257';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128098';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771668';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111890';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771684';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044325';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044329';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044334';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111891';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771691';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128099';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771702';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771727';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111892';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771736';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771759';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771770';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044356';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044363';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044385';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044389';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044401';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044403';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044406';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044415';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044428';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111893';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044433';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044499';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044503';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771786';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771788';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044518';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044537';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044540';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044546';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128101';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128102';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044558';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044579';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128202';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044582';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128203';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771793';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771803';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044583';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128206';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771810';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771817';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044618';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771826';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771843';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771847';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771853';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044649';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044662';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044666';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100111895';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044671';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044681';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128208';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128209';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044693';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771876';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771880';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771888';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044737';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771898';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100112026';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044767';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044777';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128211';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044789';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044803';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128212';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044814';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044821';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100112027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771926';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771968';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771977';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100771994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772021';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044824';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772036';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772039';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044843';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044851';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044852';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100112028';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100128213';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044858';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044883';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044892';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772043';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044899';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044917';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772064';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA100772075';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044925';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:27' WHERE product_id = 'CMA101044929';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112029';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101044936';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101044962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772079';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772140';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772141';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101044969';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101044988';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101044991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772150';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101044994';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772156';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772207';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101044998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772244';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772260';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045005';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128215';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045041';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045065';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045069';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772263';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128216';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045104';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045109';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045118';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112031';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128218';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045123';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045135';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045138';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045142';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045145';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045155';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128219';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045166';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112032';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045171';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772284';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045179';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045214';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128221';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772296';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045221';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112033';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772330';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045226';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772332';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772335';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772339';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045252';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772364';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112034';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772370';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045255';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128225';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045272';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045274';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045277';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045283';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128226';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128227';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045287';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045299';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128228';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112035';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128229';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772394';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128230';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045347';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045352';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772410';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045367';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112036';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045401';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045431';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045449';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045455';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045459';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772413';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128364';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128365';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112037';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045469';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045472';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772424';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772427';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772432';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772434';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128367';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772440';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772445';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045491';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772448';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045495';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045499';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772449';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045502';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772451';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772452';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045511';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128369';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045573';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045586';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772466';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772483';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128370';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772488';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772492';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112039';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045611';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112040';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045615';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045625';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045628';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772529';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772531';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128371';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772593';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045632';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045656';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045660';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045665';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045668';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045671';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100112041';
UPDATE products202503 SET brand_id = 'CEB001707', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA101045678';
UPDATE products202503 SET brand_id = 'CEB000380', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100128373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772594';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:46:28' WHERE product_id = 'CMA100772597';
COMMIT;
