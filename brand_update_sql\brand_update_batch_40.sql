BEGIN TRANSACTION;
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829928';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829934';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829939';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415476';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829941';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415483';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415487';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829943';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829954';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415505';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415513';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829958';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829959';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829962';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829964';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829971';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829974';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829976';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415539';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415554';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829981';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA101415575';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:12' WHERE product_id = 'CMA100829985';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415587';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100829992';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415597';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415608';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415614';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415627';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830010';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415634';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830025';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415638';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830026';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830027';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415641';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830031';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415647';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830043';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830049';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415658';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830053';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830054';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415664';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830058';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415673';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415678';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415686';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415693';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415701';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415708';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830077';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415719';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830086';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830092';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830120';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830131';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830139';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415732';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415746';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415767';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415772';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830144';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415794';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830159';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415803';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830174';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830186';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830192';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415823';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830202';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415830';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830203';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415840';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830212';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415846';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830213';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830224';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830232';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830235';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415862';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415875';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415895';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415903';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415912';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415921';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415929';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830236';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415932';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830246';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415938';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830250';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830255';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415957';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415967';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830269';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830277';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830280';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830286';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415980';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415995';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101415998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830287';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416002';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830296';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830317';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416014';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416021';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830326';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416032';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416040';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830341';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830359';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416071';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416079';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416084';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830364';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416098';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416113';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416117';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416120';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416124';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830368';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416132';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830400';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830405';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416142';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416151';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830420';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830431';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416158';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416166';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830448';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830454';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416210';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416222';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830464';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830472';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416242';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416254';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416261';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416265';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830475';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416281';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416290';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830494';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830510';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830533';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830556';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830564';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830577';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830600';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830613';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830618';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830619';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416317';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416322';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416333';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416339';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416348';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416355';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830621';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416361';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830673';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416366';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416375';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416379';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830767';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830777';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA101416383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:13' WHERE product_id = 'CMA100830779';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416386';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416390';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416399';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830818';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416408';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830877';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416419';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830879';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830881';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830889';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416429';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830921';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830927';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830936';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830960';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830961';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830963';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830965';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416445';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416451';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830969';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416465';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830973';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830976';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830991';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830993';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100830998';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416481';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416507';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831049';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831051';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416560';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416578';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416585';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831054';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831058';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831061';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416614';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831077';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831081';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831083';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831085';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831087';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416639';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416662';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416678';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831089';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416703';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416714';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831094';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831109';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831114';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831119';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831125';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416719';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416735';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831138';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831161';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831164';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416742';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831172';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831175';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416756';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416764';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416768';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831198';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416773';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416803';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416814';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416818';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416859';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416863';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416867';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831257';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831263';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416869';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831271';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831276';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831279';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416888';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416904';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831297';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831303';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416925';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831313';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831315';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831323';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831326';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831327';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831329';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416943';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101416987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831331';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417009';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417024';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417038';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831347';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831351';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417050';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831367';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417091';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831370';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831378';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831385';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831397';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417128';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831407';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417144';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831418';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417198';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417216';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417220';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831436';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831442';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831446';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417271';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417294';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831452';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831458';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417337';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA101417348';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831470';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831477';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:14' WHERE product_id = 'CMA100831479';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831482';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831485';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417359';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831492';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831494';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417372';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417376';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417383';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417385';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417392';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417395';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417398';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831500';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831511';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831518';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831525';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831542';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831561';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417401';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417504';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417506';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417508';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417541';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831567';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831581';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831593';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831617';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417653';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831623';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831631';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417665';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831637';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831645';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417674';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417691';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417725';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831659';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831667';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831677';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417751';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417783';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831688';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417811';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831705';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831710';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831714';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417822';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417864';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417878';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831720';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831724';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831726';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417882';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417890';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831729';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417899';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831731';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831750';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831759';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417912';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831763';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417938';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417944';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831765';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831766';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417956';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417966';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417972';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417979';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417986';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831780';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831788';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831789';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831790';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831792';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831796';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831798';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101417999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831807';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831811';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418028';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831832';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831842';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831845';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418042';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418064';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418072';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831847';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418080';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831862';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831870';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831872';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831875';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831885';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418100';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831886';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418104';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831888';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418116';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831894';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831900';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831913';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831919';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831922';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831927';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418132';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418146';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418153';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418167';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418193';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418200';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418211';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418217';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418223';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831933';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831941';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831946';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831950';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418225';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418235';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100831988';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418237';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832004';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418247';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832007';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832015';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418260';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832017';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832018';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832021';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832029';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832031';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832034';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832044';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832048';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832051';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832068';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418282';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418305';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832071';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832082';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418313';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418318';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418325';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832083';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418356';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832087';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832101';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418373';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832107';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418387';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418395';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832114';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418405';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832127';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832130';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832135';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832137';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832138';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418419';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418439';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418456';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832142';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832157';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418463';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418473';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832161';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418480';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832167';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832182';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418512';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418521';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832195';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA101418541';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832196';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832202';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832204';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:15' WHERE product_id = 'CMA100832205';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418549';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832207';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418556';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418571';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832214';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418574';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832222';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418579';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418580';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832230';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832233';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832236';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832238';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832242';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832250';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832252';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832258';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418581';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832259';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832267';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418582';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832270';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832273';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832274';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418591';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832277';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832281';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832283';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418610';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832285';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418620';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418631';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832296';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832301';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418640';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832302';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418652';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832308';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832310';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418672';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418688';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418716';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832312';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832318';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832321';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832322';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832325';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832328';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418720';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418743';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832331';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418757';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832344';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418774';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418791';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832351';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832353';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418795';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418806';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418813';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832362';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832374';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832376';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832381';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418819';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418827';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832387';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832393';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418844';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418854';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832396';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418858';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832402';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418884';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418893';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832407';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832410';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832415';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418906';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832421';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832423';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832424';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418935';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832434';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418950';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418987';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418994';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832453';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101418999';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832457';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832466';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832472';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832474';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419004';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832495';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419022';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832503';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832506';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832513';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419030';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832517';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419056';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832519';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832529';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419105';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419122';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832530';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419176';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419187';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832532';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419194';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419208';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832536';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419211';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832547';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419227';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832550';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832552';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832558';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832559';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832567';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832568';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419233';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419245';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419253';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832578';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832588';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832592';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832601';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832602';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832603';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832615';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419303';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832616';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419319';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832620';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832633';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832634';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419380';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419400';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419409';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832636';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419414';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832642';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832645';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832648';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832651';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832654';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832655';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832656';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832660';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832662';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832664';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419426';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419435';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419443';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832668';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832679';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419450';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832684';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832695';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419577';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419595';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419599';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832699';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419605';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832706';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832712';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419617';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832717';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419643';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832722';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA101419646';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832732';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832739';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832746';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832748';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832751';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:16' WHERE product_id = 'CMA100832752';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832755';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832758';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419649';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832760';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832784';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832787';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832788';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419747';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832794';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419756';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832805';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832807';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419773';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832809';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832829';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832851';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419799';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832856';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419806';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832859';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832860';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419857';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832861';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832868';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419866';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832871';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832883';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419876';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419894';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832897';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419899';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419916';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832901';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832907';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832919';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419928';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419945';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832923';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419955';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419982';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832933';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419989';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101419997';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832953';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832971';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420005';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832974';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420030';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420037';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832984';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420053';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832987';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832988';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832996';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100832999';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420059';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833012';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420074';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420086';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833023';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420150';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833025';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833029';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833041';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833048';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420178';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833052';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420205';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833066';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833082';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833096';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420209';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420220';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833100';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833113';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833116';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420230';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420238';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420248';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420264';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833124';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420274';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833136';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420283';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833165';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833177';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833183';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833186';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833192';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420300';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833196';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420311';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833206';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420320';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420333';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833219';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833223';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420341';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420360';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420366';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833225';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833231';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420375';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833237';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420381';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833261';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833291';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420393';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833301';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833314';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833316';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833320';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833327';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420415';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833330';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420435';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833344';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833350';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420467';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420475';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833357';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833361';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420485';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833383';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833391';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833404';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833411';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833416';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833420';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420510';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833425';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420526';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420551';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833427';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833428';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833430';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420568';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833438';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420590';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420609';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833443';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420625';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833449';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420644';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833465';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420660';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420674';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833476';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833486';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833490';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420682';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833495';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420690';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833498';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833499';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420703';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833501';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420712';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833503';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420722';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833514';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420730';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833523';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420740';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420753';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833538';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420762';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833544';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833546';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833548';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833552';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420776';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833559';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420785';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833573';
UPDATE products202503 SET brand_id = 'CEB003111', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA101420800';
UPDATE products202503 SET brand_id = 'CEB001922', updated_at = '2025-07-16 20:47:17' WHERE product_id = 'CMA100833575';
COMMIT;
